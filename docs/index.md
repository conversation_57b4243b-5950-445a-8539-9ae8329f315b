---
layout: home

hero:
  name: ccusage
  text: Claude Code Usage Analysis
  tagline: A powerful CLI tool for analyzing Claude Code usage from local JSONL files
  image:
    src: /logo.svg
    alt: ccusage logo
  actions:
    - theme: brand
      text: Get Started
      link: /guide/
    - theme: alt
      text: View on GitHub
      link: https://github.com/ryoppippi/ccusage

features:
  - icon: 📊
    title: Daily Reports
    details: View token usage and costs aggregated by date with detailed breakdowns
    link: /guide/daily-reports
  - icon: 📅
    title: Monthly Reports
    details: Analyze usage patterns over monthly periods with cost tracking
  - icon: 💬
    title: Session Reports
    details: Group usage by conversation sessions for detailed analysis
  - icon: ⏰
    title: 5-Hour Blocks
    details: Track usage within <PERSON>'s billing windows with active monitoring
  - icon: 📈
    title: Live Monitoring
    details: Real-time dashboard with progress bars and cost projections
  - icon: 🤖
    title: Model Tracking
    details: See which <PERSON> models you're using (Opus, Sonnet, etc.)
  - icon: 📋
    title: Enhanced Display
    details: Beautiful tables with responsive layout and smart formatting
  - icon: 📄
    title: JSON Output
    details: Export data in structured JSON format for programmatic use
  - icon: 💰
    title: Cost Analysis
    details: Shows estimated costs in USD for each day/month/session
  - icon: 🔄
    title: Cache Support
    details: Tracks cache creation and cache read tokens separately
  - icon: 🌐
    title: Offline Mode
    details: Use pre-cached pricing data without network connectivity
  - icon: 🔌
    title: MCP Integration
    details: Built-in Model Context Protocol server for tool integration
---

<div style="text-align: center; margin: 2rem 0;">
  <h2 style="margin-bottom: 1rem;">Support ccusage</h2>
  <p style="margin-bottom: 1.5rem;">If you find ccusage helpful, please consider sponsoring the development!</p>
  
  <h3 style="margin-bottom: 1rem;">Featured Sponsor</h3>
  <p style="margin-bottom: 1rem;">Check out these <a href="https://www.youtube.com/watch?v=TiNpzxoBPz0&lc=UgyVgQyOhfJJlheVMcB4AaABAg" target="_blank">47 Claude Code ProTips from Greg Baugues.</a></p>
  <a href="https://www.youtube.com/watch?v=TiNpzxoBPz0&lc=UgyVgQyOhfJJlheVMcB4AaABAg" target="_blank">
    <img src="/claude_code_protips_thumbnail_v1.png" alt="47 Claude Code ProTips from Greg Baugues" style="max-width: 600px; height: auto;">
  </a>
  
  <div style="margin-top: 2rem;">
    <a href="https://github.com/sponsors/ryoppippi" target="_blank">
      <img src="https://cdn.jsdelivr.net/gh/ryoppippi/sponsors@main/sponsors.svg" alt="Sponsors" style="max-width: 100%; height: auto;">
    </a>
  </div>
</div>
