# Claude Fleet Management System 开发计划

## 项目概述

基于现有 ccusage 项目，开发一个 Claude Max 账号拼车管理系统，支持多用户使用量追踪、实时监控和冲突检测。

## 核心需求

### 业务场景
- 5个人共享一个 Claude Max 账号
- 需要追踪每个人的使用量和时间习惯
- 识别高峰时段冲突
- 公平的成本分摊机制
- 实时监控当前使用状态

### 技术要求
- 基于现有 ccusage 项目扩展
- 支持命令行和 Web 界面
- 使用 Supabase 做数据持久化
- 支持 Vercel 部署
- 保持匿名性和隐私保护

## 系统架构

### 技术栈
```
Frontend (Web):
├── Next.js 14 (App Router)
├── TypeScript
├── Tailwind CSS
├── Recharts/Chart.js (数据可视化)
├── Socket.io-client (实时通信)
└── Supabase Client

Backend:
├── Supabase (PostgreSQL + Auth + Realtime)
├── Express.js (本地 Web 服务器)
├── Socket.io (WebSocket 通信)
└── 现有 ccusage 核心逻辑

CLI Extension:
├── 基于现有 gunshi 框架
├── 扩展 ccusage 命令
├── 终端 UI (基于现有实时监控)
└── 本地配置管理
```

### 数据流架构
```mermaid
graph TB
    A[Claude Code JSONL] --> B[ccusage 数据加载器]
    B --> C[Fleet Manager]
    C --> D[本地终端 UI]
    C --> E[Supabase 数据库]
    E --> F[Web 仪表板]
    E --> G[实时通知系统]
    
    H[用户操作] --> C
    I[车队配置] --> E
```

## 数据库设计

### 核心表结构

#### 1. 车队表 (fleets)
```sql
CREATE TABLE fleets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  invite_code VARCHAR(20) UNIQUE NOT NULL,
  max_members INTEGER DEFAULT 5,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  settings JSONB DEFAULT '{}',
  -- settings 包含: 成本分摊模式、使用限制、通知设置等
);
```

#### 2. 车队成员表 (fleet_members)
```sql
CREATE TABLE fleet_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fleet_id UUID REFERENCES fleets(id) ON DELETE CASCADE,
  nickname VARCHAR(50) NOT NULL,
  user_token VARCHAR(100) UNIQUE, -- 本地设备标识
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  current_session_tokens INTEGER DEFAULT 0,
  current_session_cost DECIMAL(10, 6) DEFAULT 0,
  is_active BOOLEAN DEFAULT FALSE,
  color_theme VARCHAR(20) DEFAULT 'blue',
  UNIQUE(fleet_id, nickname)
);
```

#### 3. 使用记录表 (fleet_usage_records)
```sql
CREATE TABLE fleet_usage_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES fleet_members(id),
  session_id VARCHAR(100),
  model_name VARCHAR(50),
  input_tokens INTEGER,
  output_tokens INTEGER,
  cache_creation_tokens INTEGER DEFAULT 0,
  cache_read_tokens INTEGER DEFAULT 0,
  estimated_cost DECIMAL(10, 6),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  conversation_title TEXT,
  project_name VARCHAR(100),
  window_start TIMESTAMP WITH TIME ZONE -- 5小时窗口开始时间
);
```

#### 4. 实时窗口统计表 (fleet_window_stats)
```sql
CREATE TABLE fleet_window_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES fleet_members(id),
  window_start TIMESTAMP WITH TIME ZONE,
  total_tokens INTEGER DEFAULT 0,
  total_cost DECIMAL(10, 6) DEFAULT 0,
  session_count INTEGER DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(member_id, window_start)
);
```

#### 5. 冲突检测表 (usage_conflicts)
```sql
CREATE TABLE usage_conflicts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fleet_id UUID REFERENCES fleets(id),
  conflict_time TIMESTAMP WITH TIME ZONE,
  active_members JSONB, -- 活跃成员信息
  severity VARCHAR(20) CHECK (severity IN ('low', 'medium', 'high')),
  total_concurrent_tokens INTEGER,
  resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 功能模块设计

### 1. CLI 扩展模块

#### 新增命令: `ccusage fleet`
```bash
# 车队设置向导
ccusage fleet --mode setup

# 启动终端监控界面
ccusage fleet [--fleet-id ABC123]

# 启动本地 Web 服务器
ccusage fleet --mode web [--port 3000]

# 快速加入车队
ccusage fleet --join ABC123 --nickname "张三"
```

#### 核心类设计
```typescript
// src/fleet/fleet-manager.ts
export class FleetManager {
  // 车队创建和加入
  async createFleet(name: string, maxMembers: number): Promise<Fleet>
  async joinFleet(inviteCode: string, nickname: string): Promise<Member>
  
  // 配置管理
  loadLocalConfig(): FleetConfig
  saveLocalConfig(config: FleetConfig): void
  
  // 界面启动
  async startTerminalInterface(fleetId?: string): Promise<void>
  async startWebInterface(port: number): Promise<void>
  async runSetup(): Promise<void>
}

// src/fleet/fleet-terminal-ui.ts
export class FleetTerminalUI {
  // 基于现有 LiveMonitor 扩展
  async start(): Promise<void>
  private renderFleetDashboard(data: FleetData): void
  private renderConflictWarnings(): void
  private uploadCurrentUsage(blockData: BlockData): Promise<void>
}
```

### 2. 数据同步模块

#### 实时数据上传器
```typescript
// src/fleet/data-uploader.ts
export class FleetDataUploader {
  constructor(private supabase: SupabaseClient) {}
  
  // 上传当前窗口使用数据
  async uploadWindowUsage(memberId: string, data: WindowUsageData): Promise<void>
  
  // 更新成员活跃状态
  async updateMemberActivity(memberId: string, isActive: boolean): Promise<void>
  
  // 批量上传历史数据
  async syncHistoricalData(memberId: string, records: UsageRecord[]): Promise<void>
}
```

#### 冲突检测引擎
```typescript
// src/fleet/conflict-detector.ts
export class ConflictDetector {
  // 检测当前冲突
  async detectCurrentConflicts(fleetId: string): Promise<Conflict[]>
  
  // 预测潜在冲突
  async predictConflicts(fleetId: string, timeRange: TimeRange): Promise<ConflictPrediction[]>
  
  // 生成使用建议
  generateUsageRecommendations(fleetData: FleetData): UsageRecommendation[]
}
```

### 3. Web 仪表板模块

#### 本地 Web 服务器
```typescript
// src/fleet/fleet-web-server.ts
export class FleetWebServer {
  private app = express()
  private io = new SocketIOServer()
  
  // API 路由
  setupRoutes(): void
  setupWebSocket(): void
  
  // 实时数据推送
  startFleetUpdates(fleetId: string): void
  
  async start(port: number): Promise<void>
}
```

#### React 组件设计
```typescript
// 主仪表板
components/FleetDashboard.tsx
├── FleetOverview.tsx          // 车队概览
├── MemberList.tsx             // 成员列表
├── UsageChart.tsx             // 使用量图表
├── ConflictAlert.tsx          // 冲突警告
├── CostBreakdown.tsx          // 成本分解
└── RealTimeMonitor.tsx        // 实时监控

// 分析页面
components/Analytics/
├── TimePatternChart.tsx       // 时间模式分析
├── ModelUsageChart.tsx        // 模型使用分布
├── CostTrendChart.tsx         // 成本趋势
└── ConflictHeatmap.tsx        // 冲突热力图
```

### 4. 实时通信模块

#### WebSocket 事件设计
```typescript
// 客户端 -> 服务器
interface ClientEvents {
  'join-fleet': (fleetId: string) => void
  'update-activity': (isActive: boolean) => void
  'request-fleet-data': () => void
}

// 服务器 -> 客户端
interface ServerEvents {
  'fleet-update': (data: FleetRealTimeData) => void
  'conflict-alert': (conflict: ConflictAlert) => void
  'member-joined': (member: Member) => void
  'member-left': (memberId: string) => void
}
```

## 开发阶段规划

### 阶段 1: 基础架构 (1-2 周)
**目标**: 建立基本的车队管理框架

**任务清单**:
- [ ] 设计并创建 Supabase 数据库 schema
- [ ] 扩展 ccusage CLI，添加 `fleet` 命令
- [ ] 实现 FleetManager 核心类
- [ ] 创建基础的车队创建/加入流程
- [ ] 实现本地配置管理

**交付物**:
- 可工作的 `ccusage fleet --mode setup` 命令
- 基础数据库结构
- 车队创建和加入功能

### 阶段 2: 终端界面 (1-2 周)
**目标**: 基于现有实时监控构建车队终端界面

**任务清单**:
- [ ] 扩展现有 LiveMonitor 类支持车队数据
- [ ] 实现 FleetTerminalUI 类
- [ ] 集成数据上传功能
- [ ] 添加冲突检测和警告显示
- [ ] 实现实时数据同步

**交付物**:
- 功能完整的终端车队监控界面
- 实时使用量显示
- 基础冲突检测

### 阶段 3: Web 仪表板 (2-3 周)
**目标**: 构建完整的 Web 管理后台

**任务清单**:
- [ ] 创建 Express.js 本地服务器
- [ ] 实现 WebSocket 实时通信
- [ ] 开发 React 仪表板组件
- [ ] 集成图表和数据可视化
- [ ] 实现响应式设计

**交付物**:
- 本地 Web 服务器
- 完整的车队管理仪表板
- 实时数据可视化

### 阶段 4: 高级功能 (2-3 周)
**目标**: 添加分析和优化功能

**任务清单**:
- [ ] 实现智能冲突检测算法
- [ ] 添加使用模式分析
- [ ] 开发成本分摊计算器
- [ ] 实现使用建议系统
- [ ] 添加数据导出功能

**交付物**:
- 智能冲突预测
- 详细的使用分析报告
- 自动化成本分摊

### 阶段 5: 部署和优化 (1 周)
**目标**: 准备生产环境部署

**任务清单**:
- [ ] 创建 Vercel 部署配置
- [ ] 优化性能和安全性
- [ ] 编写部署文档
- [ ] 进行端到端测试
- [ ] 准备用户手册

**交付物**:
- 可部署的 Web 应用
- 完整的文档
- 测试报告

## 技术实现细节

### 1. 基于现有代码的扩展策略

#### 利用现有 LiveMonitor
```typescript
// 扩展现有的实时监控逻辑
class FleetLiveMonitor extends LiveMonitor {
  constructor(
    private fleetId: string,
    private memberId: string,
    options: LiveMonitorOptions
  ) {
    super(options)
  }
  
  // 重写数据处理方法，添加车队上传
  protected async processBlockData(data: BlockData): Promise<void> {
    await super.processBlockData(data)
    await this.uploadToFleet(data)
  }
}
```

#### 复用数据加载逻辑
```typescript
// 利用现有的 data-loader
import { loadDailyUsageData, loadBlocksUsageData } from '../data-loader.ts'

class FleetDataProcessor {
  async syncMemberData(memberId: string): Promise<void> {
    // 使用现有加载器获取数据
    const dailyData = await loadDailyUsageData(/* options */)
    const blocksData = await loadBlocksUsageData(/* options */)
    
    // 转换并上传到车队数据库
    await this.uploadMemberHistory(memberId, dailyData, blocksData)
  }
}
```

### 2. 实时性能优化

#### 数据缓存策略
```typescript
class FleetDataCache {
  private cache = new Map<string, CacheEntry>()
  private readonly TTL = 30000 // 30秒缓存
  
  async getFleetData(fleetId: string): Promise<FleetData> {
    const cached = this.cache.get(fleetId)
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.data
    }
    
    const fresh = await this.fetchFleetData(fleetId)
    this.cache.set(fleetId, { data: fresh, timestamp: Date.now() })
    return fresh
  }
}
```

#### WebSocket 连接管理
```typescript
class FleetWebSocketManager {
  private connections = new Map<string, Set<Socket>>()
  
  joinFleet(socket: Socket, fleetId: string): void {
    if (!this.connections.has(fleetId)) {
      this.connections.set(fleetId, new Set())
    }
    this.connections.get(fleetId)!.add(socket)
    
    // 开始为这个车队推送数据
    this.startFleetUpdates(fleetId)
  }
  
  broadcastToFleet(fleetId: string, event: string, data: any): void {
    const sockets = this.connections.get(fleetId)
    if (sockets) {
      sockets.forEach(socket => socket.emit(event, data))
    }
  }
}
```

### 3. 安全和隐私考虑

#### 匿名用户系统
- 使用本地生成的 UUID 作为用户标识
- 昵称可以随时更改
- 不收集任何个人身份信息
- 支持完全删除用户数据

#### 数据加密
```typescript
class DataEncryption {
  // 敏感数据加密存储
  encryptSensitiveData(data: any): string {
    // 使用 AES 加密对话标题等敏感信息
  }
  
  // 本地配置加密
  encryptLocalConfig(config: FleetConfig): string {
    // 加密本地存储的车队配置
  }
}
```

## 用户体验设计

### 1. 命令行界面设计

#### 设置向导流程
```
🚗 Claude Fleet Setup

? What would you like to do?
  ❯ Create new fleet
    Join existing fleet  
    View my fleets
    
? Fleet name: › My Team Fleet
? Maximum members: › 5
? Cost sharing mode:
  ❯ Equal split
    Usage-based split
    Custom weights

✅ Fleet created! 
   Invite code: ABC123
   Share this code with your team members.

? What's your nickname? › Alice
✅ You've joined the fleet as "Alice"

Starting fleet monitor...
```

#### 终端监控界面
```
🚗 Claude Fleet Dashboard                    [Last updated: 14:32:15]
────────────────────────────────────────────────────────────────────

Fleet: My Team Fleet                         Members: 4/5

📊 Current 5-Hour Window (12:00 - 17:00)
Your Usage: 45,230 tokens                    Cost: $0.1356
Progress: [████████████░░░░░░░░░░░░░░░░░░] 72.4%

👥 Fleet Members (Current Window)
────────────────────────────────────────────────────────────────────
Alice           🟢 Active      45,230 tokens    $0.1356
Bob             ⚫ Idle         8,450 tokens    $0.0254  
Charlie         🟡 Recently     22,100 tokens    $0.0663
Diana           ⚫ Idle             0 tokens    $0.0000

Total Fleet Usage: 75,780 tokens              $0.2273

⚠️  Medium Activity Warning
2 members active simultaneously - consider coordinating usage

🚨 Approaching Token Limit  
Fleet total approaching 80% of recommended window limit

Press Ctrl+C to exit                         Press 'h' for help
```

### 2. Web 界面设计

#### 仪表板布局
```
┌─────────────────────────────────────────────────────────────┐
│ 🚗 Fleet Dashboard                              [Settings] │
├─────────────────────────────────────────────────────────────┤
│ Fleet: My Team Fleet    Members: 4/5    Status: 🟡 Medium  │
├─────────────────┬─────────────────┬─────────────────────────┤
│ Current Window  │ Today's Usage   │ This Month             │
│ 75,780 tokens   │ 234,567 tokens  │ $45.67                 │
│ $0.2273         │ $0.7037         │ 2.3M tokens            │
├─────────────────┴─────────────────┴─────────────────────────┤
│ 📊 Usage Timeline (Last 24 Hours)                          │
│ [Interactive Chart showing member activity over time]       │
├─────────────────────────────────────────────────────────────┤
│ 👥 Member Activity                                          │
│ [Real-time member status with activity indicators]          │
├─────────────────────────────────────────────────────────────┤
│ ⚠️ Conflicts & Recommendations                              │
│ [Conflict alerts and usage optimization suggestions]        │
└─────────────────────────────────────────────────────────────┘
```

## 测试策略

### 1. 单元测试
- 核心业务逻辑测试
- 数据处理函数测试
- 冲突检测算法测试

### 2. 集成测试
- Supabase 数据库操作测试
- WebSocket 通信测试
- CLI 命令集成测试

### 3. 端到端测试
- 完整用户流程测试
- 多用户并发测试
- 实时数据同步测试

## 部署和维护

### 1. 本地开发环境
```bash
# 环境变量配置
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_KEY=your_service_key

# 开发命令
bun run dev:fleet          # 启动开发服务器
bun run test:fleet         # 运行车队功能测试
bun run build:fleet        # 构建车队模块
```

### 2. 生产部署
- **CLI 工具**: 通过 npm 发布，用户通过 `bunx ccusage` 使用
- **Web 应用**: 部署到 Vercel，支持自定义域名
- **数据库**: Supabase 托管，自动备份和扩展

### 3. 监控和维护
- Supabase 内置监控和日志
- 错误追踪和性能监控
- 定期数据备份和清理

## 风险评估和缓解

### 1. 技术风险
**风险**: Supabase 实时功能性能限制
**缓解**: 实现本地缓存和降级策略

**风险**: 大量并发用户时的性能问题  
**缓解**: 实现连接池和数据分片

### 2. 用户体验风险
**风险**: 设置过程复杂
**缓解**: 提供详细的向导和文档

**风险**: 实时数据延迟
**缓解**: 实现乐观更新和离线支持

### 3. 数据安全风险
**风险**: 用户数据泄露
**缓解**: 最小化数据收集，实现端到端加密

## 成功指标

### 1. 功能指标
- [ ] 支持 5+ 用户同时监控
- [ ] 实时数据延迟 < 5 秒
- [ ] 冲突检测准确率 > 90%
- [ ] 系统可用性 > 99%

### 2. 用户体验指标
- [ ] 设置流程 < 5 分钟完成
- [ ] 界面响应时间 < 2 秒
- [ ] 用户满意度 > 4.5/5
- [ ] 文档完整性 100%

### 3. 技术指标
- [ ] 代码覆盖率 > 80%
- [ ] 构建时间 < 2 分钟
- [ ] 包大小增长 < 20%
- [ ] 内存使用 < 100MB

## 后续扩展计划

### 短期扩展 (3-6 个月)
- 移动端应用支持
- 更多数据可视化选项
- 高级冲突预测算法
- 集成更多 Claude 模型

### 长期扩展 (6-12 个月)
- 多车队管理
- 企业级功能
- API 开放平台
- 第三方集成支持

---

**项目负责人**: [待定]
**预计完成时间**: 8-10 周
**技术栈版本**: Node.js 20+, TypeScript 5+, Next.js 14, Supabase
**最后更新**: 2024年12月