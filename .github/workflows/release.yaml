name: npm publish

on:
  push:
    tags:
      - '*'

jobs:
  npm:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          registry-url: 'https://registry.npmjs.org'
          node-version: ${{env.NODE_VERSION}}
      - uses: oven-sh/setup-bun@735343b667d3e6f658f44d0eca948eb6282f2b76 # v2.0.2
      - run: bun install --frozen-lockfile
      - run: npm publish --provenance --no-git-checks --access public
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
          NPM_CONFIG_PROVENANCE: true
        working-directory: ${{env.PACKAGE_DIR}}

  release:
    needs:
      - npm
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - uses: oven-sh/setup-bun@735343b667d3e6f658f44d0eca948eb6282f2b76 # v2.0.2
        with:
          bun-version: latest
      - run: bun x changelogithub
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
