name: CI

on:
  push:
  pull_request:

jobs:
  ci:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: oven-sh/setup-bun@735343b667d3e6f658f44d0eca948eb6282f2b76 # v2.0.2
        with:
          bun-version: latest
      - run: bun install --frozen-lockfile
      - run: bun lint
      - run: bun typecheck
      - name: Create default Claude directories for tests
        run: |
          mkdir -p $HOME/.claude/projects
          mkdir -p $HOME/.config/claude/projects
      - run: bun run test

  npm-publish-dry-run-and-upload-pkg-pr-now:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: oven-sh/setup-bun@735343b667d3e6f658f44d0eca948eb6282f2b76 # v2.0.2
        with:
          bun-version: latest
      - run: bun install --frozen-lockfile
      - run: bunx pkg-pr-new publish

  spell-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions Repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - uses: crate-ci/typos@master
        with:
          config: ./typos.toml
