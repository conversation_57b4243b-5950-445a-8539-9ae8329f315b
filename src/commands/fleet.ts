import process from 'node:process';
import { define } from 'gunshi';
import pc from 'picocolors';
import { log, logger } from '../logger.ts';
import { FleetManager } from '../fleet/core/fleet-manager.ts';

export const fleetCommand = define({
	name: 'fleet',
	description: 'Manage Claude fleet sharing and monitoring',
	args: {
		action: {
			type: 'string',
			description: 'Action to perform: create, join, leave, status, monitor, dashboard',
			positional: true,
		},
		name: {
			type: 'string',
			short: 'n',
			description: 'Fleet name (for create action)',
		},
		code: {
			type: 'string',
			short: 'c',
			description: 'Fleet invite code (for join action)',
		},
		nickname: {
			type: 'string',
			short: 'u',
			description: 'Your nickname in the fleet',
		},
		token: {
			type: 'string',
			short: 't',
			description: 'Your Claude token for fleet sharing',
		},
		config: {
			type: 'string',
			description: 'Path to fleet configuration file',
		},
		json: {
			type: 'boolean',
			short: 'j',
			description: 'Output in JSON format',
			default: false,
		},
		verbose: {
			type: 'boolean',
			short: 'v',
			description: 'Verbose output',
			default: false,
		},
	},
	async run(ctx) {
		if (ctx.values.json) {
			logger.level = 0;
		}

		const action = ctx.values.action;
		
		if (!action) {
			logger.error('Please specify an action: create, join, leave, status, monitor, dashboard');
			process.exit(1);
		}

		try {
			const fleetManager = new FleetManager();
			
			switch (action.toLowerCase()) {
				case 'create':
					await handleCreateFleet(fleetManager, ctx);
					break;
				case 'join':
					await handleJoinFleet(fleetManager, ctx);
					break;
				case 'leave':
					await handleLeaveFleet(fleetManager, ctx);
					break;
				case 'status':
					await handleFleetStatus(fleetManager, ctx);
					break;
				case 'monitor':
					await handleFleetMonitor(fleetManager, ctx);
					break;
				case 'dashboard':
					await handleFleetDashboard(fleetManager, ctx);
					break;
				default:
					logger.error(`Unknown action: ${action}`);
					logger.info('Available actions: create, join, leave, status, monitor, dashboard');
					process.exit(1);
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error);
			const errorStack = error instanceof Error ? error.stack : undefined;

			if (ctx.values.json) {
				log(JSON.stringify({ error: errorMessage }, null, 2));
			} else {
				logger.error(`Fleet operation failed: ${errorMessage}`);
				if (ctx.values.verbose && errorStack) {
					logger.error(errorStack);
				}
			}
			process.exit(1);
		}
	},
});

async function handleCreateFleet(fleetManager: FleetManager, ctx: any) {
	const { name, nickname, token, json } = ctx.values;
	
	if (!name) {
		logger.error('Fleet name is required for create action. Use --name or -n');
		process.exit(1);
	}
	
	if (!nickname) {
		logger.error('Nickname is required. Use --nickname or -u');
		process.exit(1);
	}
	
	if (!token) {
		logger.error('Claude token is required. Use --token or -t');
		process.exit(1);
	}

	if (!json) {
		logger.info(`Creating fleet "${name}"...`);
	}

	const result = await fleetManager.createFleet(name, nickname, token);
	
	if (json) {
		log(JSON.stringify(result, null, 2));
	} else {
		logger.success(`Fleet "${name}" created successfully!`);
		logger.info(`Invite code: ${pc.cyan(result.inviteCode)}`);
		logger.info(`Share this code with your team members to join the fleet.`);
	}
}

async function handleJoinFleet(fleetManager: FleetManager, ctx: any) {
	const { code, nickname, token, json } = ctx.values;
	
	if (!code) {
		logger.error('Fleet invite code is required for join action. Use --code or -c');
		process.exit(1);
	}
	
	if (!nickname) {
		logger.error('Nickname is required. Use --nickname or -u');
		process.exit(1);
	}
	
	if (!token) {
		logger.error('Claude token is required. Use --token or -t');
		process.exit(1);
	}

	if (!json) {
		logger.info(`Joining fleet with code "${code}"...`);
	}

	const result = await fleetManager.joinFleet(code, nickname, token);
	
	if (json) {
		log(JSON.stringify(result, null, 2));
	} else {
		logger.success(`Successfully joined fleet "${result.fleetName}"!`);
		logger.info(`You are now a member of the fleet as "${nickname}".`);
	}
}

async function handleLeaveFleet(fleetManager: FleetManager, ctx: any) {
	const { json } = ctx.values;

	if (!json) {
		logger.info('Leaving current fleet...');
	}

	const result = await fleetManager.leaveFleet();
	
	if (json) {
		log(JSON.stringify(result, null, 2));
	} else {
		logger.success('Successfully left the fleet.');
	}
}

async function handleFleetStatus(fleetManager: FleetManager, ctx: any) {
	const { json } = ctx.values;

	const status = await fleetManager.getFleetStatus();
	
	if (json) {
		log(JSON.stringify(status, null, 2));
	} else {
		if (!status.isInFleet) {
			logger.info('You are not currently in any fleet.');
			logger.info('Use "ccusage fleet create" or "ccusage fleet join" to get started.');
			return;
		}

		logger.box(`Fleet Status: ${status.fleetName}`);
		logger.info(`Members: ${status.memberCount}/${status.maxMembers}`);
		logger.info(`Your nickname: ${pc.cyan(status.currentMember?.nickname || 'Unknown')}`);
		logger.info(`Active members: ${status.activeMembers}`);
		
		if (status.members && status.members.length > 0) {
			logger.info('\nFleet Members:');
			for (const member of status.members) {
				const statusIcon = member.isActive ? pc.green('●') : pc.gray('○');
				const lastSeen = member.lastActiveAt 
					? new Date(member.lastActiveAt).toLocaleString()
					: 'Never';
				logger.info(`  ${statusIcon} ${member.nickname} (last seen: ${lastSeen})`);
			}
		}
	}
}

async function handleFleetMonitor(_fleetManager: FleetManager, ctx: any) {
	const { json } = ctx.values;

	if (json) {
		logger.error('Monitor mode is not available in JSON output mode');
		process.exit(1);
	}

	logger.info('Starting fleet live monitor...');
	logger.info('This feature will be implemented in the next phase.');
	logger.info('For now, use "ccusage fleet status" to check fleet status.');
}

async function handleFleetDashboard(_fleetManager: FleetManager, ctx: any) {
	const { json } = ctx.values;

	if (json) {
		logger.error('Dashboard mode is not available in JSON output mode');
		process.exit(1);
	}

	logger.info('Starting fleet dashboard...');
	logger.info('This feature will be implemented in the next phase.');
	logger.info('For now, use "ccusage fleet status" to check fleet status.');
}
