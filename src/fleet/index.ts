/**
 * @fileoverview Fleet Management System Main Export
 * 
 * 车队管理系统的主要导出文件
 */

// 类型和接口
export * from './types.ts';
export * from './interfaces.ts';
export * from './constants.ts';
export * from './errors.ts';

// 核心类（将在后续任务中实现）
export { FleetManager } from './core/fleet-manager.ts';
export { FleetDataUploader } from './core/fleet-data-uploader.ts';
export { ConflictDetector } from './core/conflict-detector.ts';
export { PatternAnalyzer } from './core/pattern-analyzer.ts';
export { CostCalculator } from './core/cost-calculator.ts';

// UI组件
export { FleetTerminalUI } from './ui/fleet-terminal-ui.ts';
export { FleetLiveMonitor } from './ui/fleet-live-monitor.ts';

// Web服务器
export { FleetWebServer } from './web/fleet-web-server.ts';
export { FleetWebSocketManager } from './web/fleet-websocket-manager.ts';

// 工具类
export { ConfigManager } from './utils/config-manager.ts';
export { FleetDataCache } from './utils/fleet-data-cache.ts';
export { FleetValidator } from './utils/fleet-validator.ts';
export { FleetLogger } from './utils/fleet-logger.ts';

// 数据库相关
export { SupabaseFleetClient } from './database/supabase-fleet-client.ts';
export { FleetDatabase } from './database/fleet-database.ts';
