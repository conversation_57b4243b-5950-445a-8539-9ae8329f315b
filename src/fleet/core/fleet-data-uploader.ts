/**
 * @fileoverview Fleet Data Uploader Implementation
 * 
 * 车队数据上传器实现 - 负责将本地使用数据上传到Supabase
 */

import type { LoadedUsageEntry } from '../../_session-blocks.ts';
import type {
  IFleetDataUploader,
  WindowUsageData,
  MemberId,
} from '../interfaces.ts';

/**
 * 车队数据上传器实现
 * 
 * 负责：
 * - 实时数据上传
 * - 成员活跃状态更新
 * - 历史数据同步
 * - 窗口统计更新
 */
export class FleetDataUploader implements IFleetDataUploader {
  // TODO: 在阶段2中实现具体功能
  
  async uploadWindowUsage(memberId: MemberId, data: WindowUsageData): Promise<void> {
    throw new Error('Not implemented yet - will be implemented in Phase 2');
  }

  async updateMemberActivity(memberId: MemberId, isActive: boolean): Promise<void> {
    throw new Error('Not implemented yet - will be implemented in Phase 2');
  }

  async syncHistoricalData(memberId: MemberId, records: LoadedUsageEntry[]): Promise<void> {
    throw new Error('Not implemented yet - will be implemented in Phase 2');
  }

  async uploadUsageRecord(memberId: MemberId, record: LoadedUsageEntry): Promise<void> {
    throw new Error('Not implemented yet - will be implemented in Phase 2');
  }

  async updateWindowStats(memberId: MemberId, windowStart: string): Promise<void> {
    throw new Error('Not implemented yet - will be implemented in Phase 2');
  }
}
