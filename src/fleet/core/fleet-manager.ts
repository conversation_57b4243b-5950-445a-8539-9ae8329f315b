/**
 * @fileoverview Fleet Manager Core Implementation
 *
 * 车队管理器核心实现 - 负责车队的创建、加入、配置管理等核心功能
 */

import fs from 'node:fs';
import path from 'node:path';
import { homedir } from 'node:os';
import { createClient } from '@supabase/supabase-js';
import type {
  IFleetManager,
  Fleet,
  FleetMember,
  CreateFleetOptions,
  JoinFleetOptions,
  FleetConfig,
  FleetId,
  MemberId,
} from '../interfaces.ts';
import type { FleetSettings, ColorTheme } from '../types.ts';
import { SupabaseFleetClient } from '../database/supabase-fleet-client.ts';
import { FleetError, FleetConfigError, FleetNotFoundError } from '../errors.ts';

/**
 * 车队管理器实现
 *
 * 这是车队管理系统的核心类，负责：
 * - 车队的创建和管理
 * - 成员的加入和管理
 * - 本地配置的管理
 * - UI界面的启动
 */
export class FleetManager implements IFleetManager {
  private configPath: string;
  private dbClient: SupabaseFleetClient | null = null;

  constructor() {
    // 配置文件路径：~/.ccusage/fleet-config.json
    const ccusageDir = path.join(homedir(), '.ccusage');
    this.configPath = path.join(ccusageDir, 'fleet-config.json');

    // 确保配置目录存在
    if (!fs.existsSync(ccusageDir)) {
      fs.mkdirSync(ccusageDir, { recursive: true });
    }
  }

  /**
   * 初始化数据库客户端
   */
  private initializeDatabase(): void {
    if (this.dbClient) return;

    const config = this.loadLocalConfig();
    if (!config.supabaseUrl || !config.supabaseKey) {
      throw new FleetConfigError('Supabase configuration not found. Please run fleet setup first.');
    }

    const supabase = createClient(config.supabaseUrl, config.supabaseKey);
    this.dbClient = new SupabaseFleetClient(supabase);
  }

  /**
   * 生成邀请码
   */
  private generateInviteCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 获取随机颜色主题
   */
  private getRandomColorTheme(): ColorTheme {
    const themes: ColorTheme[] = ['blue', 'green', 'red', 'purple', 'orange', 'pink', 'yellow'];
    return themes[Math.floor(Math.random() * themes.length)];
  }

  async createFleet(name: string, nickname: string, token: string): Promise<{ fleetName: string; inviteCode: string }> {
    this.initializeDatabase();

    const inviteCode = this.generateInviteCode();
    const defaultSettings: FleetSettings = {
      cost_sharing_mode: 'equal',
      usage_limit_per_window: 1000000, // 1M tokens per 5-hour window
      notifications_enabled: true,
      alert_thresholds: {
        concurrent_users: 3,
        token_usage_percent: 80,
        cost_threshold: 50,
      },
    };

    const fleetData = {
      name,
      invite_code: inviteCode,
      max_members: 5,
      settings: defaultSettings,
      is_active: true,
    };

    try {
      const fleet = await this.dbClient!.createFleet(fleetData);

      // 创建者自动成为第一个成员
      const memberData = {
        fleet_id: fleet.id,
        nickname,
        user_token: token,
        current_session_tokens: 0,
        current_session_cost: 0,
        is_active: true,
        color_theme: this.getRandomColorTheme(),
      };

      await this.dbClient!.addMember(memberData);

      // 保存到本地配置
      const config = this.loadLocalConfig();
      config.currentFleet = {
        fleetId: fleet.id,
        memberId: '', // 将在addMember返回后更新
        nickname,
        userToken: token,
      };
      this.saveLocalConfig(config);

      return {
        fleetName: fleet.name,
        inviteCode: fleet.invite_code,
      };
    } catch (error) {
      throw new FleetError(`Failed to create fleet: ${error.message}`);
    }
  }

  async joinFleet(inviteCode: string, nickname: string, token: string): Promise<{ fleetName: string }> {
    this.initializeDatabase();

    try {
      // 通过邀请码查找车队
      const fleet = await this.dbClient!.getFleetByInviteCode(inviteCode);
      if (!fleet) {
        throw new FleetNotFoundError(`Fleet with invite code "${inviteCode}" not found`);
      }

      if (!fleet.is_active) {
        throw new FleetError('This fleet is no longer active');
      }

      // 检查车队是否已满
      const members = await this.dbClient!.getFleetMembers(fleet.id);
      if (members.length >= fleet.max_members) {
        throw new FleetError('Fleet is full');
      }

      // 检查昵称是否已被使用
      const existingMember = members.find(m => m.nickname === nickname);
      if (existingMember) {
        throw new FleetError(`Nickname "${nickname}" is already taken in this fleet`);
      }

      // 添加成员
      const memberData = {
        fleet_id: fleet.id,
        nickname,
        user_token: token,
        current_session_tokens: 0,
        current_session_cost: 0,
        is_active: true,
        color_theme: this.getRandomColorTheme(),
      };

      const member = await this.dbClient!.addMember(memberData);

      // 保存到本地配置
      const config = this.loadLocalConfig();
      config.currentFleet = {
        fleetId: fleet.id,
        memberId: member.id,
        nickname,
        userToken: token,
      };
      this.saveLocalConfig(config);

      return {
        fleetName: fleet.name,
      };
    } catch (error) {
      if (error instanceof FleetError || error instanceof FleetNotFoundError) {
        throw error;
      }
      throw new FleetError(`Failed to join fleet: ${error.message}`);
    }
  }

  async leaveFleet(): Promise<{ success: boolean }> {
    const config = this.loadLocalConfig();
    if (!config.currentFleet) {
      throw new FleetError('You are not currently in any fleet');
    }

    this.initializeDatabase();

    try {
      await this.dbClient!.removeMember(config.currentFleet.memberId);

      // 清除本地配置
      config.currentFleet = null;
      this.saveLocalConfig(config);

      return { success: true };
    } catch (error) {
      throw new FleetError(`Failed to leave fleet: ${error.message}`);
    }
  }

  async getFleetStatus(): Promise<{
    isInFleet: boolean;
    fleetName?: string;
    memberCount?: number;
    maxMembers?: number;
    activeMembers?: number;
    currentMember?: { nickname: string };
    members?: Array<{ nickname: string; isActive: boolean; lastActiveAt?: string }>;
  }> {
    const config = this.loadLocalConfig();
    if (!config.currentFleet) {
      return { isInFleet: false };
    }

    this.initializeDatabase();

    try {
      const fleet = await this.dbClient!.getFleet(config.currentFleet.fleetId);
      if (!fleet) {
        // 车队不存在，清除本地配置
        config.currentFleet = null;
        this.saveLocalConfig(config);
        return { isInFleet: false };
      }

      const members = await this.dbClient!.getFleetMembers(fleet.id);
      const activeMembers = members.filter(m => m.is_active).length;

      return {
        isInFleet: true,
        fleetName: fleet.name,
        memberCount: members.length,
        maxMembers: fleet.max_members,
        activeMembers,
        currentMember: { nickname: config.currentFleet.nickname },
        members: members.map(m => ({
          nickname: m.nickname,
          isActive: m.is_active,
          lastActiveAt: m.last_active,
        })),
      };
    } catch (error) {
      throw new FleetError(`Failed to get fleet status: ${error.message}`);
    }
  }

  async getFleet(fleetId: FleetId): Promise<Fleet | null> {
    this.initializeDatabase();
    return await this.dbClient!.getFleet(fleetId);
  }

  async getFleetMembers(fleetId: FleetId): Promise<FleetMember[]> {
    this.initializeDatabase();
    return await this.dbClient!.getFleetMembers(fleetId);
  }

  async updateMember(memberId: MemberId, updates: Partial<FleetMember>): Promise<FleetMember> {
    this.initializeDatabase();
    return await this.dbClient!.updateMember(memberId, updates);
  }

  loadLocalConfig(): FleetConfig {
    try {
      if (!fs.existsSync(this.configPath)) {
        // 创建默认配置
        const defaultConfig: FleetConfig = {
          supabaseUrl: '',
          supabaseKey: '',
          currentFleet: null,
          preferences: {
            autoUpload: true,
            uploadInterval: 30000, // 30 seconds
            showNotifications: true,
            colorTheme: 'blue',
          },
        };
        this.saveLocalConfig(defaultConfig);
        return defaultConfig;
      }

      const configData = fs.readFileSync(this.configPath, 'utf-8');
      return JSON.parse(configData);
    } catch (error) {
      throw new FleetConfigError(`Failed to load configuration: ${error.message}`);
    }
  }

  saveLocalConfig(config: FleetConfig): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      throw new FleetConfigError(`Failed to save configuration: ${error.message}`);
    }
  }

  async startTerminalInterface(fleetId?: FleetId): Promise<void> {
    const { FleetTerminalUI } = await import('../ui/fleet-terminal-ui.ts');
    const ui = new FleetTerminalUI();
    await ui.start();
  }

  async startWebInterface(port: number): Promise<void> {
    const { FleetWebServer } = await import('../web/fleet-web-server.ts');
    const server = new FleetWebServer();
    await server.start(port);
  }

  async runSetup(): Promise<void> {
    // TODO: 实现设置向导
    throw new FleetError('Setup wizard not implemented yet');
  }
}
