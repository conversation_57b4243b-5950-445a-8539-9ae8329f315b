/**
 * @fileoverview Fleet Management System Core Interfaces
 * 
 * 定义车队管理系统的核心接口
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import type { LoadedUsageEntry } from '../_session-blocks.ts';
import type {
  Fleet,
  FleetMember,
  FleetUsageRecord,
  FleetWindowStats,
  UsageConflict,
  FleetConfig,
  FleetRealTimeData,
  CreateFleetOptions,
  JoinFleetOptions,
  UsageRecommendation,
  ConflictPrediction,
  TimeRange,
  UsagePattern,
  CostBreakdown,
  FleetId,
  MemberId,
  InviteCode,
  UserToken,
} from './types.ts';

// ============================================================================
// 车队管理器接口
// ============================================================================

/**
 * 车队管理器核心接口
 */
export interface IFleetManager {
  /**
   * 创建新车队
   */
  createFleet(options: CreateFleetOptions): Promise<Fleet>;

  /**
   * 加入现有车队
   */
  joinFleet(options: JoinFleetOptions): Promise<FleetMember>;

  /**
   * 离开车队
   */
  leaveFleet(fleetId: FleetId, memberId: MemberId): Promise<void>;

  /**
   * 获取车队信息
   */
  getFleet(fleetId: FleetId): Promise<Fleet | null>;

  /**
   * 获取车队成员列表
   */
  getFleetMembers(fleetId: FleetId): Promise<FleetMember[]>;

  /**
   * 更新成员信息
   */
  updateMember(memberId: MemberId, updates: Partial<FleetMember>): Promise<FleetMember>;

  /**
   * 加载本地配置
   */
  loadLocalConfig(): FleetConfig;

  /**
   * 保存本地配置
   */
  saveLocalConfig(config: FleetConfig): void;

  /**
   * 启动终端界面
   */
  startTerminalInterface(fleetId?: FleetId): Promise<void>;

  /**
   * 启动Web界面
   */
  startWebInterface(port: number): Promise<void>;

  /**
   * 运行设置向导
   */
  runSetup(): Promise<void>;
}

// ============================================================================
// 数据上传器接口
// ============================================================================

/**
 * 车队数据上传器接口
 */
export interface IFleetDataUploader {
  /**
   * 上传当前窗口使用数据
   */
  uploadWindowUsage(memberId: MemberId, data: WindowUsageData): Promise<void>;

  /**
   * 更新成员活跃状态
   */
  updateMemberActivity(memberId: MemberId, isActive: boolean): Promise<void>;

  /**
   * 批量上传历史数据
   */
  syncHistoricalData(memberId: MemberId, records: LoadedUsageEntry[]): Promise<void>;

  /**
   * 上传单条使用记录
   */
  uploadUsageRecord(memberId: MemberId, record: LoadedUsageEntry): Promise<void>;

  /**
   * 更新窗口统计
   */
  updateWindowStats(memberId: MemberId, windowStart: string): Promise<void>;
}

/**
 * 窗口使用数据
 */
export interface WindowUsageData {
  window_start: string;
  total_tokens: number;
  input_tokens: number;
  output_tokens: number;
  cache_creation_tokens: number;
  cache_read_tokens: number;
  total_cost: number;
  session_count: number;
  models_used: string[];
  projects: string[];
}

// ============================================================================
// 冲突检测器接口
// ============================================================================

/**
 * 冲突检测器接口
 */
export interface IConflictDetector {
  /**
   * 检测当前冲突
   */
  detectCurrentConflicts(fleetId: FleetId): Promise<UsageConflict[]>;

  /**
   * 预测潜在冲突
   */
  predictConflicts(fleetId: FleetId, timeRange: TimeRange): Promise<ConflictPrediction[]>;

  /**
   * 生成使用建议
   */
  generateUsageRecommendations(fleetData: FleetRealTimeData): UsageRecommendation[];

  /**
   * 记录冲突
   */
  recordConflict(conflict: Omit<UsageConflict, 'id' | 'created_at'>): Promise<UsageConflict>;

  /**
   * 解决冲突
   */
  resolveConflict(conflictId: string, resolutionNotes?: string): Promise<void>;
}

// ============================================================================
// 模式分析器接口
// ============================================================================

/**
 * 使用模式分析器接口
 */
export interface IPatternAnalyzer {
  /**
   * 分析成员使用模式
   */
  analyzeMemberPattern(memberId: MemberId, timeRange: TimeRange): Promise<UsagePattern>;

  /**
   * 分析车队整体模式
   */
  analyzeFleetPattern(fleetId: FleetId, timeRange: TimeRange): Promise<UsagePattern[]>;

  /**
   * 预测使用高峰
   */
  predictUsagePeaks(fleetId: FleetId, days: number): Promise<{
    date: string;
    hour: number;
    predicted_concurrent_users: number;
    confidence: number;
  }[]>;

  /**
   * 生成使用报告
   */
  generateUsageReport(fleetId: FleetId, timeRange: TimeRange): Promise<{
    summary: {
      total_tokens: number;
      total_cost: number;
      active_days: number;
      peak_concurrent_users: number;
    };
    member_breakdown: UsagePattern[];
    recommendations: UsageRecommendation[];
  }>;
}

// ============================================================================
// 成本计算器接口
// ============================================================================

/**
 * 成本分摊计算器接口
 */
export interface ICostCalculator {
  /**
   * 计算成本分摊
   */
  calculateCostBreakdown(fleetId: FleetId, timeRange: TimeRange): Promise<CostBreakdown>;

  /**
   * 计算单个成员应付金额
   */
  calculateMemberShare(
    memberId: MemberId,
    fleetId: FleetId,
    timeRange: TimeRange
  ): Promise<{
    member: FleetMember;
    usage_cost: number;
    share_amount: number;
    share_percentage: number;
  }>;

  /**
   * 预估未来成本
   */
  estimateFutureCost(
    fleetId: FleetId,
    days: number
  ): Promise<{
    estimated_total: number;
    confidence_interval: [number, number];
    per_member_estimate: Record<MemberId, number>;
  }>;
}

// ============================================================================
// 终端UI接口
// ============================================================================

/**
 * 车队终端UI接口
 */
export interface IFleetTerminalUI {
  /**
   * 启动终端界面
   */
  start(): Promise<void>;

  /**
   * 停止终端界面
   */
  stop(): void;

  /**
   * 渲染车队仪表板
   */
  renderFleetDashboard(data: FleetRealTimeData): void;

  /**
   * 渲染冲突警告
   */
  renderConflictWarnings(conflicts: UsageConflict[]): void;

  /**
   * 处理用户输入
   */
  handleUserInput(input: string): Promise<void>;
}

// ============================================================================
// Web服务器接口
// ============================================================================

/**
 * 车队Web服务器接口
 */
export interface IFleetWebServer {
  /**
   * 启动服务器
   */
  start(port: number): Promise<void>;

  /**
   * 停止服务器
   */
  stop(): Promise<void>;

  /**
   * 设置API路由
   */
  setupRoutes(): void;

  /**
   * 设置WebSocket
   */
  setupWebSocket(): void;

  /**
   * 开始车队数据更新推送
   */
  startFleetUpdates(fleetId: FleetId): void;

  /**
   * 停止车队数据更新推送
   */
  stopFleetUpdates(fleetId: FleetId): void;
}

// ============================================================================
// 数据缓存接口
// ============================================================================

/**
 * 数据缓存接口
 */
export interface IFleetDataCache {
  /**
   * 获取车队数据
   */
  getFleetData(fleetId: FleetId): Promise<FleetRealTimeData | null>;

  /**
   * 设置车队数据
   */
  setFleetData(fleetId: FleetId, data: FleetRealTimeData): Promise<void>;

  /**
   * 清除缓存
   */
  clearCache(fleetId?: FleetId): Promise<void>;

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number;
    hit_rate: number;
    miss_rate: number;
  };
}

// ============================================================================
// 配置管理接口
// ============================================================================

/**
 * 配置管理器接口
 */
export interface IConfigManager {
  /**
   * 加载配置
   */
  loadConfig(): FleetConfig;

  /**
   * 保存配置
   */
  saveConfig(config: FleetConfig): void;

  /**
   * 获取默认配置
   */
  getDefaultConfig(): FleetConfig;

  /**
   * 验证配置
   */
  validateConfig(config: FleetConfig): boolean;

  /**
   * 迁移配置
   */
  migrateConfig(oldVersion: string, newVersion: string): void;
}
