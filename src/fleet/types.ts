/**
 * @fileoverview Fleet Management System Type Definitions
 * 
 * 定义车队管理系统的核心类型和接口
 */

import type { LoadedUsageEntry } from '../_session-blocks.ts';

// ============================================================================
// 基础类型定义
// ============================================================================

/**
 * 车队ID类型
 */
export type FleetId = string;

/**
 * 成员ID类型
 */
export type MemberId = string;

/**
 * 用户令牌类型（用于匿名认证）
 */
export type UserToken = string;

/**
 * 邀请码类型
 */
export type InviteCode = string;

/**
 * 成本分摊模式
 */
export type CostSharingMode = 'equal' | 'usage_based' | 'custom_weights';

/**
 * 冲突严重程度
 */
export type ConflictSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * 冲突类型
 */
export type ConflictType = 'concurrent_usage' | 'rate_limit_approach' | 'cost_threshold' | 'token_limit';

/**
 * 成员颜色主题
 */
export type ColorTheme = 'blue' | 'green' | 'red' | 'purple' | 'orange' | 'pink' | 'yellow' | 'gray';

// ============================================================================
// 数据库实体类型
// ============================================================================

/**
 * 车队实体
 */
export interface Fleet {
  id: FleetId;
  name: string;
  invite_code: InviteCode;
  max_members: number;
  created_at: string;
  updated_at: string;
  settings: FleetSettings;
  is_active: boolean;
  description?: string;
}

/**
 * 车队设置
 */
export interface FleetSettings {
  cost_sharing_mode: CostSharingMode;
  usage_limit_per_window: number;
  notifications_enabled: boolean;
  custom_weights?: Record<MemberId, number>;
  alert_thresholds?: {
    concurrent_users: number;
    token_usage_percent: number;
    cost_threshold: number;
  };
}

/**
 * 车队成员实体
 */
export interface FleetMember {
  id: MemberId;
  fleet_id: FleetId;
  nickname: string;
  user_token: UserToken;
  joined_at: string;
  last_active: string;
  current_session_tokens: number;
  current_session_cost: number;
  is_active: boolean;
  color_theme: ColorTheme;
  total_tokens_used: number;
  total_cost_incurred: number;
  preferences: MemberPreferences;
}

/**
 * 成员偏好设置
 */
export interface MemberPreferences {
  notifications_enabled?: boolean;
  conflict_alerts?: boolean;
  usage_reports?: boolean;
  theme_preference?: 'light' | 'dark' | 'auto';
}

/**
 * 使用记录实体
 */
export interface FleetUsageRecord {
  id: string;
  member_id: MemberId;
  session_id: string;
  model_name: string;
  input_tokens: number;
  output_tokens: number;
  cache_creation_tokens: number;
  cache_read_tokens: number;
  estimated_cost: number;
  timestamp: string;
  conversation_title?: string;
  project_name?: string;
  window_start: string;
  total_tokens: number;
  raw_data?: any;
}

/**
 * 窗口统计实体
 */
export interface FleetWindowStats {
  id: string;
  member_id: MemberId;
  window_start: string;
  window_end: string;
  total_tokens: number;
  input_tokens: number;
  output_tokens: number;
  cache_creation_tokens: number;
  cache_read_tokens: number;
  total_cost: number;
  session_count: number;
  unique_projects: number;
  models_used: string[];
  last_updated: string;
}

/**
 * 冲突记录实体
 */
export interface UsageConflict {
  id: string;
  fleet_id: FleetId;
  conflict_time: string;
  conflict_type: ConflictType;
  severity: ConflictSeverity;
  active_members: ConflictMemberInfo[];
  total_concurrent_tokens: number;
  total_concurrent_cost: number;
  window_start?: string;
  details: Record<string, any>;
  resolved: boolean;
  resolved_at?: string;
  resolution_notes?: string;
  created_at: string;
}

/**
 * 冲突中的成员信息
 */
export interface ConflictMemberInfo {
  member_id: MemberId;
  nickname: string;
  current_tokens: number;
  current_cost: number;
  session_count: number;
}

// ============================================================================
// 业务逻辑类型
// ============================================================================

/**
 * 车队创建选项
 */
export interface CreateFleetOptions {
  name: string;
  description?: string;
  max_members?: number;
  settings?: Partial<FleetSettings>;
}

/**
 * 加入车队选项
 */
export interface JoinFleetOptions {
  invite_code: InviteCode;
  nickname: string;
  color_theme?: ColorTheme;
  preferences?: MemberPreferences;
}

/**
 * 车队配置
 */
export interface FleetConfig {
  fleet_id?: FleetId;
  member_id?: MemberId;
  user_token?: UserToken;
  nickname?: string;
  supabase_url?: string;
  supabase_anon_key?: string;
  local_settings?: {
    auto_upload: boolean;
    upload_interval: number;
    cache_duration: number;
  };
}

/**
 * 实时车队数据
 */
export interface FleetRealTimeData {
  fleet: Fleet;
  members: FleetMember[];
  current_window_stats: FleetWindowStats[];
  active_conflicts: UsageConflict[];
  fleet_totals: {
    total_members: number;
    active_members: number;
    current_window_tokens: number;
    current_window_cost: number;
    total_fleet_tokens: number;
    total_fleet_cost: number;
  };
}

/**
 * 使用建议
 */
export interface UsageRecommendation {
  type: 'time_suggestion' | 'usage_limit' | 'cost_warning' | 'conflict_avoidance';
  priority: 'low' | 'medium' | 'high';
  title: string;
  message: string;
  suggested_action?: string;
  estimated_savings?: number;
}

/**
 * 冲突预测
 */
export interface ConflictPrediction {
  predicted_time: string;
  conflict_type: ConflictType;
  probability: number; // 0-1
  affected_members: string[];
  suggested_mitigation: string;
}

/**
 * 时间范围
 */
export interface TimeRange {
  start: string;
  end: string;
}

/**
 * 使用模式分析结果
 */
export interface UsagePattern {
  member_id: MemberId;
  peak_hours: number[]; // 0-23
  average_session_duration: number; // minutes
  preferred_models: string[];
  daily_usage_trend: {
    date: string;
    tokens: number;
    cost: number;
  }[];
  weekly_pattern: {
    day_of_week: number; // 0-6
    average_tokens: number;
    average_cost: number;
  }[];
}

/**
 * 成本分摊结果
 */
export interface CostBreakdown {
  total_cost: number;
  sharing_mode: CostSharingMode;
  member_shares: {
    member_id: MemberId;
    nickname: string;
    usage_tokens: number;
    usage_cost: number;
    share_amount: number;
    share_percentage: number;
  }[];
  calculation_details: {
    base_cost: number;
    adjustments: Record<string, number>;
    calculation_method: string;
  };
}
