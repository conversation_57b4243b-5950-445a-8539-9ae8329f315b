/**
 * @fileoverview Supabase Fleet Client Implementation
 *
 * Supabase车队客户端实现 - 封装Supabase操作
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import type {
  Fleet,
  FleetMember,
  FleetUsageRecord,
  FleetWindowStats,
  UsageConflict,
  FleetId,
  MemberId,
  InviteCode,
} from '../types.ts';
import { FleetError, FleetNotFoundError } from '../errors.ts';

/**
 * Supabase车队客户端
 *
 * 封装所有Supabase数据库操作：
 * - 车队CRUD操作
 * - 成员管理
 * - 使用记录管理
 * - 统计数据查询
 * - 实时订阅
 */
export class SupabaseFleetClient {
  private supabase: SupabaseClient;

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  // 车队操作
  async createFleet(fleet: Omit<Fleet, 'id' | 'created_at' | 'updated_at'>): Promise<Fleet> {
    try {
      const { data, error } = await this.supabase
        .from('fleets')
        .insert([fleet])
        .select()
        .single();

      if (error) {
        throw new FleetError(`Failed to create fleet: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async getFleet(fleetId: FleetId): Promise<Fleet | null> {
    try {
      const { data, error } = await this.supabase
        .from('fleets')
        .select('*')
        .eq('id', fleetId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        throw new FleetError(`Failed to get fleet: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async getFleetByInviteCode(inviteCode: InviteCode): Promise<Fleet | null> {
    try {
      const { data, error } = await this.supabase
        .from('fleets')
        .select('*')
        .eq('invite_code', inviteCode)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // No rows returned
          return null;
        }
        throw new FleetError(`Failed to get fleet by invite code: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async updateFleet(fleetId: FleetId, updates: Partial<Fleet>): Promise<Fleet> {
    try {
      const { data, error } = await this.supabase
        .from('fleets')
        .update(updates)
        .eq('id', fleetId)
        .select()
        .single();

      if (error) {
        throw new FleetError(`Failed to update fleet: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  // 成员操作
  async addMember(member: Omit<FleetMember, 'id' | 'joined_at' | 'last_active'>): Promise<FleetMember> {
    try {
      const { data, error } = await this.supabase
        .from('fleet_members')
        .insert([member])
        .select()
        .single();

      if (error) {
        throw new FleetError(`Failed to add member: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async getFleetMembers(fleetId: FleetId): Promise<FleetMember[]> {
    try {
      const { data, error } = await this.supabase
        .from('fleet_members')
        .select('*')
        .eq('fleet_id', fleetId)
        .order('joined_at', { ascending: true });

      if (error) {
        throw new FleetError(`Failed to get fleet members: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async updateMember(memberId: MemberId, updates: Partial<FleetMember>): Promise<FleetMember> {
    try {
      const { data, error } = await this.supabase
        .from('fleet_members')
        .update(updates)
        .eq('id', memberId)
        .select()
        .single();

      if (error) {
        throw new FleetError(`Failed to update member: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async removeMember(memberId: MemberId): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('fleet_members')
        .delete()
        .eq('id', memberId);

      if (error) {
        throw new FleetError(`Failed to remove member: ${error.message}`);
      }
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  // 使用记录操作
  async insertUsageRecord(record: Omit<FleetUsageRecord, 'id'>): Promise<FleetUsageRecord> {
    try {
      const { data, error } = await this.supabase
        .from('fleet_usage_records')
        .insert([record])
        .select()
        .single();

      if (error) {
        throw new FleetError(`Failed to insert usage record: ${error.message}`);
      }

      return data;
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async getUsageRecords(memberId: MemberId, windowStart?: string): Promise<FleetUsageRecord[]> {
    try {
      let query = this.supabase
        .from('fleet_usage_records')
        .select('*')
        .eq('member_id', memberId)
        .order('timestamp', { ascending: false });

      if (windowStart) {
        query = query.gte('timestamp', windowStart);
      }

      const { data, error } = await query;

      if (error) {
        throw new FleetError(`Failed to get usage records: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  // 统计数据操作
  async getWindowStats(fleetId: FleetId, windowStart?: string): Promise<FleetWindowStats[]> {
    try {
      let query = this.supabase
        .from('fleet_window_stats')
        .select('*')
        .eq('fleet_id', fleetId)
        .order('window_start', { ascending: false });

      if (windowStart) {
        query = query.eq('window_start', windowStart);
      }

      const { data, error } = await query;

      if (error) {
        throw new FleetError(`Failed to get window stats: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  async getConflicts(fleetId: FleetId, activeOnly?: boolean): Promise<UsageConflict[]> {
    try {
      let query = this.supabase
        .from('usage_conflicts')
        .select('*')
        .eq('fleet_id', fleetId)
        .order('detected_at', { ascending: false });

      if (activeOnly) {
        query = query.eq('is_resolved', false);
      }

      const { data, error } = await query;

      if (error) {
        throw new FleetError(`Failed to get conflicts: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      if (error instanceof FleetError) throw error;
      throw new FleetError(`Database error: ${error.message}`);
    }
  }

  // 实时订阅
  subscribeToFleetChanges(fleetId: FleetId, callback: (payload: any) => void): () => void {
    const subscription = this.supabase
      .channel(`fleet-${fleetId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'fleet_members', filter: `fleet_id=eq.${fleetId}` },
        callback
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'fleet_usage_records', filter: `fleet_id=eq.${fleetId}` },
        callback
      )
      .subscribe();

    return () => {
      this.supabase.removeChannel(subscription);
    };
  }
}
