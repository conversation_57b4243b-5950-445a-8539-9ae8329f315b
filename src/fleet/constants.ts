/**
 * @fileoverview Fleet Management System Constants
 * 
 * 定义车队管理系统的常量和配置
 */

// ============================================================================
// 时间常量
// ============================================================================

/**
 * 5小时窗口持续时间（毫秒）
 */
export const WINDOW_DURATION_MS = 5 * 60 * 60 * 1000;

/**
 * 5小时窗口持续时间（小时）
 */
export const WINDOW_DURATION_HOURS = 5;

/**
 * 数据刷新间隔（毫秒）
 */
export const DATA_REFRESH_INTERVAL_MS = 30 * 1000; // 30秒

/**
 * 活跃状态超时时间（毫秒）
 */
export const ACTIVITY_TIMEOUT_MS = 5 * 60 * 1000; // 5分钟

/**
 * 缓存TTL（毫秒）
 */
export const CACHE_TTL_MS = 30 * 1000; // 30秒

// ============================================================================
// 限制常量
// ============================================================================

/**
 * 车队最大成员数
 */
export const MAX_FLEET_MEMBERS = 20;

/**
 * 车队最小成员数
 */
export const MIN_FLEET_MEMBERS = 1;

/**
 * 默认车队成员数限制
 */
export const DEFAULT_MAX_MEMBERS = 5;

/**
 * 昵称最大长度
 */
export const MAX_NICKNAME_LENGTH = 50;

/**
 * 车队名称最大长度
 */
export const MAX_FLEET_NAME_LENGTH = 100;

/**
 * 邀请码长度
 */
export const INVITE_CODE_LENGTH = 8;

/**
 * 默认每窗口token限制
 */
export const DEFAULT_WINDOW_TOKEN_LIMIT = 100000;

// ============================================================================
// 冲突检测阈值
// ============================================================================

/**
 * 并发用户警告阈值
 */
export const CONCURRENT_USERS_WARNING_THRESHOLD = 3;

/**
 * 并发用户严重警告阈值
 */
export const CONCURRENT_USERS_CRITICAL_THRESHOLD = 4;

/**
 * Token使用量警告阈值（百分比）
 */
export const TOKEN_USAGE_WARNING_THRESHOLD = 0.8;

/**
 * Token使用量严重警告阈值（百分比）
 */
export const TOKEN_USAGE_CRITICAL_THRESHOLD = 0.9;

/**
 * 成本警告阈值（美元）
 */
export const COST_WARNING_THRESHOLD = 10.0;

/**
 * 成本严重警告阈值（美元）
 */
export const COST_CRITICAL_THRESHOLD = 20.0;

// ============================================================================
// 默认设置
// ============================================================================

/**
 * 默认车队设置
 */
export const DEFAULT_FLEET_SETTINGS = {
  cost_sharing_mode: 'equal' as const,
  usage_limit_per_window: DEFAULT_WINDOW_TOKEN_LIMIT,
  notifications_enabled: true,
  alert_thresholds: {
    concurrent_users: CONCURRENT_USERS_WARNING_THRESHOLD,
    token_usage_percent: TOKEN_USAGE_WARNING_THRESHOLD,
    cost_threshold: COST_WARNING_THRESHOLD,
  },
};

/**
 * 默认成员偏好
 */
export const DEFAULT_MEMBER_PREFERENCES = {
  notifications_enabled: true,
  conflict_alerts: true,
  usage_reports: true,
  theme_preference: 'auto' as const,
};

/**
 * 默认本地设置
 */
export const DEFAULT_LOCAL_SETTINGS = {
  auto_upload: true,
  upload_interval: 60, // 秒
  cache_duration: 300, // 秒
};

/**
 * 可用的颜色主题
 */
export const COLOR_THEMES = [
  'blue',
  'green', 
  'red',
  'purple',
  'orange',
  'pink',
  'yellow',
  'gray',
] as const;

// ============================================================================
// API端点
// ============================================================================

/**
 * Fleet API端点
 */
export const FLEET_API_ENDPOINTS = {
  // 车队管理
  CREATE_FLEET: '/api/fleet/create',
  JOIN_FLEET: '/api/fleet/join',
  LEAVE_FLEET: '/api/fleet/leave',
  GET_FLEET: '/api/fleet/:id',
  UPDATE_FLEET: '/api/fleet/:id',
  
  // 成员管理
  GET_MEMBERS: '/api/fleet/:id/members',
  UPDATE_MEMBER: '/api/fleet/:id/members/:memberId',
  
  // 数据上传
  UPLOAD_USAGE: '/api/fleet/:id/usage',
  UPDATE_ACTIVITY: '/api/fleet/:id/activity',
  
  // 统计和分析
  GET_STATS: '/api/fleet/:id/stats',
  GET_CONFLICTS: '/api/fleet/:id/conflicts',
  GET_PATTERNS: '/api/fleet/:id/patterns',
  GET_COST_BREAKDOWN: '/api/fleet/:id/costs',
} as const;

/**
 * WebSocket事件
 */
export const WEBSOCKET_EVENTS = {
  // 客户端 -> 服务器
  JOIN_FLEET: 'join-fleet',
  LEAVE_FLEET: 'leave-fleet',
  UPDATE_ACTIVITY: 'update-activity',
  REQUEST_FLEET_DATA: 'request-fleet-data',
  
  // 服务器 -> 客户端
  FLEET_UPDATE: 'fleet-update',
  CONFLICT_ALERT: 'conflict-alert',
  MEMBER_JOINED: 'member-joined',
  MEMBER_LEFT: 'member-left',
  MEMBER_ACTIVITY_CHANGED: 'member-activity-changed',
  USAGE_UPDATE: 'usage-update',
} as const;

// ============================================================================
// 错误代码
// ============================================================================

/**
 * Fleet错误代码
 */
export const FLEET_ERROR_CODES = {
  // 车队相关错误
  FLEET_NOT_FOUND: 'FLEET_NOT_FOUND',
  FLEET_FULL: 'FLEET_FULL',
  FLEET_INACTIVE: 'FLEET_INACTIVE',
  INVALID_INVITE_CODE: 'INVALID_INVITE_CODE',
  
  // 成员相关错误
  MEMBER_NOT_FOUND: 'MEMBER_NOT_FOUND',
  MEMBER_ALREADY_EXISTS: 'MEMBER_ALREADY_EXISTS',
  NICKNAME_TAKEN: 'NICKNAME_TAKEN',
  INVALID_USER_TOKEN: 'INVALID_USER_TOKEN',
  
  // 权限错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // 数据错误
  INVALID_DATA: 'INVALID_DATA',
  DATA_UPLOAD_FAILED: 'DATA_UPLOAD_FAILED',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // 配置错误
  INVALID_CONFIG: 'INVALID_CONFIG',
  CONFIG_LOAD_FAILED: 'CONFIG_LOAD_FAILED',
  CONFIG_SAVE_FAILED: 'CONFIG_SAVE_FAILED',
  
  // 网络错误
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  TIMEOUT: 'TIMEOUT',
  RATE_LIMITED: 'RATE_LIMITED',
} as const;

// ============================================================================
// 文件路径
// ============================================================================

/**
 * 配置文件路径
 */
export const CONFIG_PATHS = {
  FLEET_CONFIG: '.ccusage-fleet.json',
  USER_TOKEN_FILE: '.ccusage-fleet-token',
  CACHE_DIR: '.ccusage-fleet-cache',
} as const;

// ============================================================================
// 正则表达式
// ============================================================================

/**
 * 邀请码格式验证
 */
export const INVITE_CODE_REGEX = /^[A-Z0-9]{6,20}$/;

/**
 * 昵称格式验证（允许中文、英文、数字、下划线、连字符）
 */
export const NICKNAME_REGEX = /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/;

/**
 * 用户令牌格式验证
 */
export const USER_TOKEN_REGEX = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;

// ============================================================================
// 显示格式
// ============================================================================

/**
 * 数字格式化选项
 */
export const NUMBER_FORMAT_OPTIONS = {
  TOKENS: {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
  COST: {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 4,
    maximumFractionDigits: 6,
  },
  PERCENTAGE: {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  },
} as const;

/**
 * 日期格式化选项
 */
export const DATE_FORMAT_OPTIONS = {
  SHORT_DATE: {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  } as const,
  SHORT_TIME: {
    hour: '2-digit',
    minute: '2-digit',
  } as const,
  FULL_DATETIME: {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  } as const,
} as const;

// ============================================================================
// 终端UI常量
// ============================================================================

/**
 * 终端UI颜色
 */
export const TERMINAL_COLORS = {
  PRIMARY: '\x1b[36m',    // 青色
  SUCCESS: '\x1b[32m',    // 绿色
  WARNING: '\x1b[33m',    // 黄色
  ERROR: '\x1b[31m',      // 红色
  INFO: '\x1b[34m',       // 蓝色
  RESET: '\x1b[0m',       // 重置
  BOLD: '\x1b[1m',        // 粗体
  DIM: '\x1b[2m',         // 暗淡
} as const;

/**
 * 终端UI符号
 */
export const TERMINAL_SYMBOLS = {
  FLEET: '🚗',
  MEMBER: '👤',
  ACTIVE: '🟢',
  IDLE: '⚫',
  WARNING: '⚠️',
  ERROR: '🚨',
  SUCCESS: '✅',
  INFO: 'ℹ️',
  TOKENS: '🪙',
  COST: '💰',
  TIME: '⏰',
  CHART: '📊',
} as const;
