/**
 * @fileoverview Fleet Management System Error Classes
 * 
 * 定义车队管理系统的错误类型
 */

import { FLEET_ERROR_CODES } from './constants.ts';

// ============================================================================
// 基础错误类
// ============================================================================

/**
 * Fleet系统基础错误类
 */
export class FleetError extends Error {
  public readonly code: string;
  public readonly details?: Record<string, any>;

  constructor(
    message: string,
    code: string = FLEET_ERROR_CODES.INVALID_DATA,
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'FleetError';
    this.code = code;
    this.details = details;
    
    // 确保错误堆栈正确显示
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, FleetError);
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      details: this.details,
      stack: this.stack,
    };
  }
}

// ============================================================================
// 车队相关错误
// ============================================================================

/**
 * 车队未找到错误
 */
export class FleetNotFoundError extends FleetError {
  constructor(fleetId: string) {
    super(
      `Fleet with ID "${fleetId}" not found`,
      FLEET_ERROR_CODES.FLEET_NOT_FOUND,
      { fleetId }
    );
    this.name = 'FleetNotFoundError';
  }
}

/**
 * 车队已满错误
 */
export class FleetFullError extends FleetError {
  constructor(fleetId: string, maxMembers: number, currentMembers: number) {
    super(
      `Fleet "${fleetId}" is full (${currentMembers}/${maxMembers} members)`,
      FLEET_ERROR_CODES.FLEET_FULL,
      { fleetId, maxMembers, currentMembers }
    );
    this.name = 'FleetFullError';
  }
}

/**
 * 车队未激活错误
 */
export class FleetInactiveError extends FleetError {
  constructor(fleetId: string) {
    super(
      `Fleet "${fleetId}" is inactive`,
      FLEET_ERROR_CODES.FLEET_INACTIVE,
      { fleetId }
    );
    this.name = 'FleetInactiveError';
  }
}

/**
 * 无效邀请码错误
 */
export class InvalidInviteCodeError extends FleetError {
  constructor(inviteCode: string) {
    super(
      `Invalid invite code: "${inviteCode}"`,
      FLEET_ERROR_CODES.INVALID_INVITE_CODE,
      { inviteCode }
    );
    this.name = 'InvalidInviteCodeError';
  }
}

// ============================================================================
// 成员相关错误
// ============================================================================

/**
 * 成员未找到错误
 */
export class MemberNotFoundError extends FleetError {
  constructor(memberId: string) {
    super(
      `Member with ID "${memberId}" not found`,
      FLEET_ERROR_CODES.MEMBER_NOT_FOUND,
      { memberId }
    );
    this.name = 'MemberNotFoundError';
  }
}

/**
 * 成员已存在错误
 */
export class MemberAlreadyExistsError extends FleetError {
  constructor(userToken: string, fleetId: string) {
    super(
      `Member with token "${userToken}" already exists in fleet "${fleetId}"`,
      FLEET_ERROR_CODES.MEMBER_ALREADY_EXISTS,
      { userToken, fleetId }
    );
    this.name = 'MemberAlreadyExistsError';
  }
}

/**
 * 昵称已被占用错误
 */
export class NicknameTakenError extends FleetError {
  constructor(nickname: string, fleetId: string) {
    super(
      `Nickname "${nickname}" is already taken in fleet "${fleetId}"`,
      FLEET_ERROR_CODES.NICKNAME_TAKEN,
      { nickname, fleetId }
    );
    this.name = 'NicknameTakenError';
  }
}

/**
 * 无效用户令牌错误
 */
export class InvalidUserTokenError extends FleetError {
  constructor(userToken: string) {
    super(
      `Invalid user token: "${userToken}"`,
      FLEET_ERROR_CODES.INVALID_USER_TOKEN,
      { userToken }
    );
    this.name = 'InvalidUserTokenError';
  }
}

// ============================================================================
// 权限相关错误
// ============================================================================

/**
 * 未授权错误
 */
export class UnauthorizedError extends FleetError {
  constructor(action: string, resource?: string) {
    super(
      `Unauthorized to perform action: ${action}${resource ? ` on ${resource}` : ''}`,
      FLEET_ERROR_CODES.UNAUTHORIZED,
      { action, resource }
    );
    this.name = 'UnauthorizedError';
  }
}

/**
 * 权限不足错误
 */
export class InsufficientPermissionsError extends FleetError {
  constructor(requiredPermission: string, action: string) {
    super(
      `Insufficient permissions: "${requiredPermission}" required for action: ${action}`,
      FLEET_ERROR_CODES.INSUFFICIENT_PERMISSIONS,
      { requiredPermission, action }
    );
    this.name = 'InsufficientPermissionsError';
  }
}

// ============================================================================
// 数据相关错误
// ============================================================================

/**
 * 数据上传失败错误
 */
export class DataUploadFailedError extends FleetError {
  constructor(reason: string, details?: Record<string, any>) {
    super(
      `Data upload failed: ${reason}`,
      FLEET_ERROR_CODES.DATA_UPLOAD_FAILED,
      details
    );
    this.name = 'DataUploadFailedError';
  }
}

/**
 * 数据库错误
 */
export class DatabaseError extends FleetError {
  constructor(operation: string, originalError?: Error) {
    super(
      `Database operation failed: ${operation}`,
      FLEET_ERROR_CODES.DATABASE_ERROR,
      { 
        operation,
        originalError: originalError?.message,
        originalStack: originalError?.stack,
      }
    );
    this.name = 'DatabaseError';
  }
}

/**
 * 无效数据错误
 */
export class InvalidDataError extends FleetError {
  constructor(field: string, value: any, expectedFormat?: string) {
    super(
      `Invalid data for field "${field}": ${value}${expectedFormat ? `. Expected: ${expectedFormat}` : ''}`,
      FLEET_ERROR_CODES.INVALID_DATA,
      { field, value, expectedFormat }
    );
    this.name = 'InvalidDataError';
  }
}

// ============================================================================
// 配置相关错误
// ============================================================================

/**
 * 配置加载失败错误
 */
export class ConfigLoadFailedError extends FleetError {
  constructor(configPath: string, reason: string) {
    super(
      `Failed to load config from "${configPath}": ${reason}`,
      FLEET_ERROR_CODES.CONFIG_LOAD_FAILED,
      { configPath, reason }
    );
    this.name = 'ConfigLoadFailedError';
  }
}

/**
 * 配置保存失败错误
 */
export class ConfigSaveFailedError extends FleetError {
  constructor(configPath: string, reason: string) {
    super(
      `Failed to save config to "${configPath}": ${reason}`,
      FLEET_ERROR_CODES.CONFIG_SAVE_FAILED,
      { configPath, reason }
    );
    this.name = 'ConfigSaveFailedError';
  }
}

/**
 * 无效配置错误
 */
export class InvalidConfigError extends FleetError {
  constructor(field: string, issue: string) {
    super(
      `Invalid configuration for "${field}": ${issue}`,
      FLEET_ERROR_CODES.INVALID_CONFIG,
      { field, issue }
    );
    this.name = 'InvalidConfigError';
  }
}

// ============================================================================
// 网络相关错误
// ============================================================================

/**
 * 连接失败错误
 */
export class ConnectionFailedError extends FleetError {
  constructor(endpoint: string, reason?: string) {
    super(
      `Connection failed to "${endpoint}"${reason ? `: ${reason}` : ''}`,
      FLEET_ERROR_CODES.CONNECTION_FAILED,
      { endpoint, reason }
    );
    this.name = 'ConnectionFailedError';
  }
}

/**
 * 超时错误
 */
export class TimeoutError extends FleetError {
  constructor(operation: string, timeoutMs: number) {
    super(
      `Operation "${operation}" timed out after ${timeoutMs}ms`,
      FLEET_ERROR_CODES.TIMEOUT,
      { operation, timeoutMs }
    );
    this.name = 'TimeoutError';
  }
}

/**
 * 速率限制错误
 */
export class RateLimitedError extends FleetError {
  constructor(retryAfterMs?: number) {
    super(
      `Rate limited${retryAfterMs ? `. Retry after ${retryAfterMs}ms` : ''}`,
      FLEET_ERROR_CODES.RATE_LIMITED,
      { retryAfterMs }
    );
    this.name = 'RateLimitedError';
  }
}

// ============================================================================
// 错误工具函数
// ============================================================================

/**
 * 判断是否为Fleet错误
 */
export function isFleetError(error: any): error is FleetError {
  return error instanceof FleetError;
}

/**
 * 从错误对象创建Fleet错误
 */
export function createFleetErrorFromUnknown(error: unknown, defaultMessage = 'Unknown error'): FleetError {
  if (isFleetError(error)) {
    return error;
  }
  
  if (error instanceof Error) {
    return new FleetError(error.message, FLEET_ERROR_CODES.INVALID_DATA, {
      originalError: error.message,
      originalStack: error.stack,
    });
  }
  
  return new FleetError(defaultMessage, FLEET_ERROR_CODES.INVALID_DATA, {
    originalError: String(error),
  });
}

/**
 * 错误代码到错误类的映射
 */
export const ERROR_CODE_TO_CLASS = {
  [FLEET_ERROR_CODES.FLEET_NOT_FOUND]: FleetNotFoundError,
  [FLEET_ERROR_CODES.FLEET_FULL]: FleetFullError,
  [FLEET_ERROR_CODES.FLEET_INACTIVE]: FleetInactiveError,
  [FLEET_ERROR_CODES.INVALID_INVITE_CODE]: InvalidInviteCodeError,
  [FLEET_ERROR_CODES.MEMBER_NOT_FOUND]: MemberNotFoundError,
  [FLEET_ERROR_CODES.MEMBER_ALREADY_EXISTS]: MemberAlreadyExistsError,
  [FLEET_ERROR_CODES.NICKNAME_TAKEN]: NicknameTakenError,
  [FLEET_ERROR_CODES.INVALID_USER_TOKEN]: InvalidUserTokenError,
  [FLEET_ERROR_CODES.UNAUTHORIZED]: UnauthorizedError,
  [FLEET_ERROR_CODES.INSUFFICIENT_PERMISSIONS]: InsufficientPermissionsError,
  [FLEET_ERROR_CODES.DATA_UPLOAD_FAILED]: DataUploadFailedError,
  [FLEET_ERROR_CODES.DATABASE_ERROR]: DatabaseError,
  [FLEET_ERROR_CODES.INVALID_DATA]: InvalidDataError,
  [FLEET_ERROR_CODES.CONFIG_LOAD_FAILED]: ConfigLoadFailedError,
  [FLEET_ERROR_CODES.CONFIG_SAVE_FAILED]: ConfigSaveFailedError,
  [FLEET_ERROR_CODES.INVALID_CONFIG]: InvalidConfigError,
  [FLEET_ERROR_CODES.CONNECTION_FAILED]: ConnectionFailedError,
  [FLEET_ERROR_CODES.TIMEOUT]: TimeoutError,
  [FLEET_ERROR_CODES.RATE_LIMITED]: RateLimitedError,
} as const;
