-- Claude Fleet Management System Database Schema
-- 基于 Supabase PostgreSQL 的车队管理数据库设计
-- 创建时间: 2025-01-28

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 1. 车队表 (fleets)
-- 存储车队的基本信息和配置
CREATE TABLE fleets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  invite_code VARCHAR(20) UNIQUE NOT NULL,
  max_members INTEGER DEFAULT 5 CHECK (max_members > 0 AND max_members <= 20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  settings JSONB DEFAULT '{}',
  -- settings 包含: 成本分摊模式、使用限制、通知设置等
  -- 示例: {"cost_sharing_mode": "equal", "usage_limit_per_window": 100000, "notifications_enabled": true}
  is_active BOOLEAN DEFAULT TRUE,
  description TEXT,
  
  -- 索引
  CONSTRAINT fleets_name_length CHECK (char_length(name) >= 1),
  CONSTRAINT fleets_invite_code_format CHECK (invite_code ~ '^[A-Z0-9]{6,20}$')
);

-- 创建索引
CREATE INDEX idx_fleets_invite_code ON fleets(invite_code);
CREATE INDEX idx_fleets_active ON fleets(is_active) WHERE is_active = TRUE;

-- 2. 车队成员表 (fleet_members)
-- 存储车队成员信息和当前状态
CREATE TABLE fleet_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fleet_id UUID NOT NULL REFERENCES fleets(id) ON DELETE CASCADE,
  nickname VARCHAR(50) NOT NULL,
  user_token VARCHAR(100) UNIQUE NOT NULL, -- 本地设备标识，用于匿名认证
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  current_session_tokens INTEGER DEFAULT 0 CHECK (current_session_tokens >= 0),
  current_session_cost DECIMAL(10, 6) DEFAULT 0 CHECK (current_session_cost >= 0),
  is_active BOOLEAN DEFAULT FALSE, -- 当前是否在使用 Claude
  color_theme VARCHAR(20) DEFAULT 'blue',
  total_tokens_used BIGINT DEFAULT 0 CHECK (total_tokens_used >= 0),
  total_cost_incurred DECIMAL(12, 6) DEFAULT 0 CHECK (total_cost_incurred >= 0),
  preferences JSONB DEFAULT '{}',
  -- preferences 包含: 通知偏好、界面设置等
  
  -- 约束
  UNIQUE(fleet_id, nickname),
  UNIQUE(fleet_id, user_token),
  CONSTRAINT fleet_members_nickname_length CHECK (char_length(nickname) >= 1),
  CONSTRAINT fleet_members_color_theme_valid CHECK (
    color_theme IN ('blue', 'green', 'red', 'purple', 'orange', 'pink', 'yellow', 'gray')
  )
);

-- 创建索引
CREATE INDEX idx_fleet_members_fleet_id ON fleet_members(fleet_id);
CREATE INDEX idx_fleet_members_user_token ON fleet_members(user_token);
CREATE INDEX idx_fleet_members_active ON fleet_members(fleet_id, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_fleet_members_last_active ON fleet_members(last_active DESC);

-- 3. 使用记录表 (fleet_usage_records)
-- 存储详细的使用记录，基于现有 ccusage 数据结构
CREATE TABLE fleet_usage_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL REFERENCES fleet_members(id) ON DELETE CASCADE,
  session_id VARCHAR(100) NOT NULL,
  model_name VARCHAR(50) NOT NULL,
  input_tokens INTEGER NOT NULL CHECK (input_tokens >= 0),
  output_tokens INTEGER NOT NULL CHECK (output_tokens >= 0),
  cache_creation_tokens INTEGER DEFAULT 0 CHECK (cache_creation_tokens >= 0),
  cache_read_tokens INTEGER DEFAULT 0 CHECK (cache_read_tokens >= 0),
  estimated_cost DECIMAL(10, 6) NOT NULL CHECK (estimated_cost >= 0),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  conversation_title TEXT,
  project_name VARCHAR(100),
  window_start TIMESTAMP WITH TIME ZONE NOT NULL, -- 5小时窗口开始时间
  raw_data JSONB, -- 存储原始的 ccusage 数据，便于调试和扩展
  
  -- 计算总 token 数的生成列
  total_tokens INTEGER GENERATED ALWAYS AS (
    input_tokens + output_tokens + cache_creation_tokens + cache_read_tokens
  ) STORED,
  
  -- 约束
  CONSTRAINT fleet_usage_records_session_id_length CHECK (char_length(session_id) >= 1),
  CONSTRAINT fleet_usage_records_model_name_length CHECK (char_length(model_name) >= 1)
);

-- 创建索引
CREATE INDEX idx_fleet_usage_records_member_id ON fleet_usage_records(member_id);
CREATE INDEX idx_fleet_usage_records_timestamp ON fleet_usage_records(timestamp DESC);
CREATE INDEX idx_fleet_usage_records_window_start ON fleet_usage_records(window_start);
CREATE INDEX idx_fleet_usage_records_session ON fleet_usage_records(member_id, session_id);
CREATE INDEX idx_fleet_usage_records_model ON fleet_usage_records(model_name);
CREATE INDEX idx_fleet_usage_records_project ON fleet_usage_records(project_name) WHERE project_name IS NOT NULL;

-- 复合索引用于窗口查询
CREATE INDEX idx_fleet_usage_records_window_member ON fleet_usage_records(member_id, window_start, timestamp);

-- 4. 实时窗口统计表 (fleet_window_stats)
-- 存储每个成员在特定时间窗口内的聚合统计数据
CREATE TABLE fleet_window_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID NOT NULL REFERENCES fleet_members(id) ON DELETE CASCADE,
  window_start TIMESTAMP WITH TIME ZONE NOT NULL,
  window_end TIMESTAMP WITH TIME ZONE NOT NULL,
  total_tokens INTEGER DEFAULT 0 CHECK (total_tokens >= 0),
  input_tokens INTEGER DEFAULT 0 CHECK (input_tokens >= 0),
  output_tokens INTEGER DEFAULT 0 CHECK (output_tokens >= 0),
  cache_creation_tokens INTEGER DEFAULT 0 CHECK (cache_creation_tokens >= 0),
  cache_read_tokens INTEGER DEFAULT 0 CHECK (cache_read_tokens >= 0),
  total_cost DECIMAL(10, 6) DEFAULT 0 CHECK (total_cost >= 0),
  session_count INTEGER DEFAULT 0 CHECK (session_count >= 0),
  unique_projects INTEGER DEFAULT 0 CHECK (unique_projects >= 0),
  models_used JSONB DEFAULT '[]', -- 使用的模型列表
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- 约束
  UNIQUE(member_id, window_start),
  CONSTRAINT fleet_window_stats_window_valid CHECK (window_end > window_start)
);

-- 创建索引
CREATE INDEX idx_fleet_window_stats_member_window ON fleet_window_stats(member_id, window_start);
CREATE INDEX idx_fleet_window_stats_window_start ON fleet_window_stats(window_start DESC);
CREATE INDEX idx_fleet_window_stats_last_updated ON fleet_window_stats(last_updated DESC);

-- 5. 冲突检测表 (usage_conflicts)
-- 记录检测到的使用冲突和警告
CREATE TABLE usage_conflicts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fleet_id UUID NOT NULL REFERENCES fleets(id) ON DELETE CASCADE,
  conflict_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  conflict_type VARCHAR(20) NOT NULL CHECK (
    conflict_type IN ('concurrent_usage', 'rate_limit_approach', 'cost_threshold', 'token_limit')
  ),
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  active_members JSONB NOT NULL, -- 涉及的活跃成员信息
  -- 示例: [{"member_id": "uuid", "nickname": "Alice", "current_tokens": 1000}]
  total_concurrent_tokens INTEGER DEFAULT 0 CHECK (total_concurrent_tokens >= 0),
  total_concurrent_cost DECIMAL(10, 6) DEFAULT 0 CHECK (total_concurrent_cost >= 0),
  window_start TIMESTAMP WITH TIME ZONE,
  details JSONB DEFAULT '{}', -- 冲突详细信息
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- 约束
  CONSTRAINT usage_conflicts_active_members_valid CHECK (jsonb_array_length(active_members) > 0)
);

-- 创建索引
CREATE INDEX idx_usage_conflicts_fleet_id ON usage_conflicts(fleet_id);
CREATE INDEX idx_usage_conflicts_time ON usage_conflicts(conflict_time DESC);
CREATE INDEX idx_usage_conflicts_severity ON usage_conflicts(severity, conflict_time DESC);
CREATE INDEX idx_usage_conflicts_unresolved ON usage_conflicts(fleet_id, resolved) WHERE resolved = FALSE;
CREATE INDEX idx_usage_conflicts_type ON usage_conflicts(conflict_type, conflict_time DESC);

-- 6. 车队邀请表 (fleet_invitations) - 可选，用于管理邀请链接
CREATE TABLE fleet_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fleet_id UUID NOT NULL REFERENCES fleets(id) ON DELETE CASCADE,
  invite_code VARCHAR(20) NOT NULL,
  created_by UUID REFERENCES fleet_members(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  max_uses INTEGER DEFAULT 1 CHECK (max_uses > 0),
  current_uses INTEGER DEFAULT 0 CHECK (current_uses >= 0),
  is_active BOOLEAN DEFAULT TRUE,

  -- 约束
  CONSTRAINT fleet_invitations_uses_valid CHECK (current_uses <= max_uses),
  CONSTRAINT fleet_invitations_expiry_valid CHECK (expires_at IS NULL OR expires_at > created_at)
);

-- 创建索引
CREATE INDEX idx_fleet_invitations_code ON fleet_invitations(invite_code);
CREATE INDEX idx_fleet_invitations_fleet ON fleet_invitations(fleet_id);
CREATE INDEX idx_fleet_invitations_active ON fleet_invitations(is_active, expires_at) WHERE is_active = TRUE;

-- ============================================================================
-- 触发器和函数
-- ============================================================================

-- 更新 updated_at 字段的通用函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 fleets 表添加 updated_at 触发器
CREATE TRIGGER update_fleets_updated_at
    BEFORE UPDATE ON fleets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 自动更新成员统计的函数
CREATE OR REPLACE FUNCTION update_member_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新成员的总使用量和总成本
    UPDATE fleet_members
    SET
        total_tokens_used = total_tokens_used + NEW.total_tokens,
        total_cost_incurred = total_cost_incurred + NEW.estimated_cost,
        last_active = NEW.timestamp
    WHERE id = NEW.member_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 fleet_usage_records 表添加统计更新触发器
CREATE TRIGGER update_member_totals_trigger
    AFTER INSERT ON fleet_usage_records
    FOR EACH ROW
    EXECUTE FUNCTION update_member_totals();

-- 自动生成邀请码的函数
CREATE OR REPLACE FUNCTION generate_invite_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..8 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ language 'plpgsql';

-- 为新车队自动生成邀请码的触发器函数
CREATE OR REPLACE FUNCTION set_fleet_invite_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invite_code IS NULL OR NEW.invite_code = '' THEN
        LOOP
            NEW.invite_code := generate_invite_code();
            -- 检查是否重复
            IF NOT EXISTS (SELECT 1 FROM fleets WHERE invite_code = NEW.invite_code) THEN
                EXIT;
            END IF;
        END LOOP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 fleets 表添加邀请码生成触发器
CREATE TRIGGER set_fleet_invite_code_trigger
    BEFORE INSERT ON fleets
    FOR EACH ROW
    EXECUTE FUNCTION set_fleet_invite_code();

-- 窗口统计聚合函数
CREATE OR REPLACE FUNCTION update_window_stats(
    p_member_id UUID,
    p_window_start TIMESTAMP WITH TIME ZONE
)
RETURNS VOID AS $$
DECLARE
    window_end TIMESTAMP WITH TIME ZONE;
    stats_record RECORD;
BEGIN
    -- 计算窗口结束时间（5小时窗口）
    window_end := p_window_start + INTERVAL '5 hours';

    -- 聚合该窗口内的统计数据
    SELECT
        COUNT(*) as session_count,
        SUM(total_tokens) as total_tokens,
        SUM(input_tokens) as input_tokens,
        SUM(output_tokens) as output_tokens,
        SUM(cache_creation_tokens) as cache_creation_tokens,
        SUM(cache_read_tokens) as cache_read_tokens,
        SUM(estimated_cost) as total_cost,
        COUNT(DISTINCT project_name) as unique_projects,
        jsonb_agg(DISTINCT model_name) as models_used
    INTO stats_record
    FROM fleet_usage_records
    WHERE member_id = p_member_id
      AND timestamp >= p_window_start
      AND timestamp < window_end;

    -- 插入或更新窗口统计
    INSERT INTO fleet_window_stats (
        member_id, window_start, window_end,
        total_tokens, input_tokens, output_tokens,
        cache_creation_tokens, cache_read_tokens,
        total_cost, session_count, unique_projects, models_used
    ) VALUES (
        p_member_id, p_window_start, window_end,
        COALESCE(stats_record.total_tokens, 0),
        COALESCE(stats_record.input_tokens, 0),
        COALESCE(stats_record.output_tokens, 0),
        COALESCE(stats_record.cache_creation_tokens, 0),
        COALESCE(stats_record.cache_read_tokens, 0),
        COALESCE(stats_record.total_cost, 0),
        COALESCE(stats_record.session_count, 0),
        COALESCE(stats_record.unique_projects, 0),
        COALESCE(stats_record.models_used, '[]'::jsonb)
    )
    ON CONFLICT (member_id, window_start)
    DO UPDATE SET
        window_end = EXCLUDED.window_end,
        total_tokens = EXCLUDED.total_tokens,
        input_tokens = EXCLUDED.input_tokens,
        output_tokens = EXCLUDED.output_tokens,
        cache_creation_tokens = EXCLUDED.cache_creation_tokens,
        cache_read_tokens = EXCLUDED.cache_read_tokens,
        total_cost = EXCLUDED.total_cost,
        session_count = EXCLUDED.session_count,
        unique_projects = EXCLUDED.unique_projects,
        models_used = EXCLUDED.models_used,
        last_updated = NOW();
END;
$$ language 'plpgsql';

-- ============================================================================
-- Row Level Security (RLS) 策略
-- ============================================================================

-- 启用 RLS
ALTER TABLE fleets ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_usage_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_window_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_conflicts ENABLE ROW LEVEL SECURITY;
ALTER TABLE fleet_invitations ENABLE ROW LEVEL SECURITY;

-- 车队访问策略：成员只能访问自己所在的车队
CREATE POLICY "Members can view their fleets" ON fleets
    FOR SELECT USING (
        id IN (
            SELECT fleet_id FROM fleet_members
            WHERE user_token = current_setting('app.current_user_token', true)
        )
    );

CREATE POLICY "Members can update their fleets" ON fleets
    FOR UPDATE USING (
        id IN (
            SELECT fleet_id FROM fleet_members
            WHERE user_token = current_setting('app.current_user_token', true)
        )
    );

-- 成员访问策略：只能访问同车队的成员信息
CREATE POLICY "Members can view fleet members" ON fleet_members
    FOR SELECT USING (
        fleet_id IN (
            SELECT fleet_id FROM fleet_members
            WHERE user_token = current_setting('app.current_user_token', true)
        )
    );

CREATE POLICY "Members can update their own info" ON fleet_members
    FOR UPDATE USING (
        user_token = current_setting('app.current_user_token', true)
    );

-- 使用记录访问策略：只能访问同车队成员的记录
CREATE POLICY "Members can view fleet usage records" ON fleet_usage_records
    FOR SELECT USING (
        member_id IN (
            SELECT fm1.id FROM fleet_members fm1
            JOIN fleet_members fm2 ON fm1.fleet_id = fm2.fleet_id
            WHERE fm2.user_token = current_setting('app.current_user_token', true)
        )
    );

CREATE POLICY "Members can insert their own usage records" ON fleet_usage_records
    FOR INSERT WITH CHECK (
        member_id IN (
            SELECT id FROM fleet_members
            WHERE user_token = current_setting('app.current_user_token', true)
        )
    );

-- 窗口统计访问策略
CREATE POLICY "Members can view fleet window stats" ON fleet_window_stats
    FOR SELECT USING (
        member_id IN (
            SELECT fm1.id FROM fleet_members fm1
            JOIN fleet_members fm2 ON fm1.fleet_id = fm2.fleet_id
            WHERE fm2.user_token = current_setting('app.current_user_token', true)
        )
    );

-- 冲突记录访问策略
CREATE POLICY "Members can view fleet conflicts" ON usage_conflicts
    FOR SELECT USING (
        fleet_id IN (
            SELECT fleet_id FROM fleet_members
            WHERE user_token = current_setting('app.current_user_token', true)
        )
    );

-- ============================================================================
-- 有用的视图
-- ============================================================================

-- 车队概览视图
CREATE VIEW fleet_overview AS
SELECT
    f.id,
    f.name,
    f.invite_code,
    f.max_members,
    f.created_at,
    f.settings,
    f.description,
    COUNT(fm.id) as current_members,
    COUNT(CASE WHEN fm.is_active THEN 1 END) as active_members,
    SUM(fm.total_tokens_used) as total_fleet_tokens,
    SUM(fm.total_cost_incurred) as total_fleet_cost
FROM fleets f
LEFT JOIN fleet_members fm ON f.id = fm.fleet_id
GROUP BY f.id, f.name, f.invite_code, f.max_members, f.created_at, f.settings, f.description;

-- 当前窗口活动视图
CREATE VIEW current_window_activity AS
WITH current_windows AS (
    SELECT
        member_id,
        window_start,
        total_tokens,
        total_cost,
        session_count,
        last_updated,
        ROW_NUMBER() OVER (PARTITION BY member_id ORDER BY window_start DESC) as rn
    FROM fleet_window_stats
    WHERE window_start <= NOW() AND (window_start + INTERVAL '5 hours') > NOW()
)
SELECT
    fm.id as member_id,
    fm.fleet_id,
    fm.nickname,
    fm.is_active,
    fm.color_theme,
    COALESCE(cw.total_tokens, 0) as current_window_tokens,
    COALESCE(cw.total_cost, 0) as current_window_cost,
    COALESCE(cw.session_count, 0) as current_window_sessions,
    cw.window_start,
    cw.last_updated
FROM fleet_members fm
LEFT JOIN current_windows cw ON fm.id = cw.member_id AND cw.rn = 1;

-- 冲突警告视图
CREATE VIEW active_conflicts AS
SELECT
    uc.*,
    f.name as fleet_name,
    EXTRACT(EPOCH FROM (NOW() - uc.conflict_time))/60 as minutes_ago
FROM usage_conflicts uc
JOIN fleets f ON uc.fleet_id = f.id
WHERE uc.resolved = FALSE
  AND uc.conflict_time > NOW() - INTERVAL '1 hour'
ORDER BY uc.conflict_time DESC;

-- ============================================================================
-- 示例数据 (可选，用于测试)
-- ============================================================================

-- 插入示例车队
INSERT INTO fleets (name, description, max_members, settings) VALUES
('开发团队车队', '我们的开发团队共享Claude Max账号', 5, '{"cost_sharing_mode": "equal", "usage_limit_per_window": 100000, "notifications_enabled": true}'),
('研究小组', '研究项目使用的Claude账号', 3, '{"cost_sharing_mode": "usage_based", "usage_limit_per_window": 50000, "notifications_enabled": false}');

-- 注意：实际使用时，成员数据应该通过应用程序API插入，这里仅作为结构示例

-- ============================================================================
-- 实用函数
-- ============================================================================

-- 获取车队当前窗口统计
CREATE OR REPLACE FUNCTION get_fleet_current_window_stats(p_fleet_id UUID)
RETURNS TABLE (
    member_id UUID,
    nickname VARCHAR(50),
    is_active BOOLEAN,
    current_tokens INTEGER,
    current_cost DECIMAL(10,6),
    window_start TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        cwa.member_id,
        cwa.nickname,
        cwa.is_active,
        cwa.current_window_tokens,
        cwa.current_window_cost,
        cwa.window_start
    FROM current_window_activity cwa
    WHERE cwa.fleet_id = p_fleet_id
    ORDER BY cwa.current_window_tokens DESC;
END;
$$ language 'plpgsql';

-- 检测潜在冲突
CREATE OR REPLACE FUNCTION detect_potential_conflicts(p_fleet_id UUID)
RETURNS TABLE (
    conflict_type TEXT,
    severity TEXT,
    message TEXT,
    affected_members JSONB
) AS $$
DECLARE
    active_count INTEGER;
    total_tokens INTEGER;
    fleet_settings JSONB;
    usage_limit INTEGER;
BEGIN
    -- 获取车队设置
    SELECT settings INTO fleet_settings FROM fleets WHERE id = p_fleet_id;
    usage_limit := COALESCE((fleet_settings->>'usage_limit_per_window')::INTEGER, 100000);

    -- 检查当前活跃成员数量
    SELECT COUNT(*) INTO active_count
    FROM fleet_members
    WHERE fleet_id = p_fleet_id AND is_active = TRUE;

    -- 检查当前窗口总使用量
    SELECT COALESCE(SUM(current_window_tokens), 0) INTO total_tokens
    FROM current_window_activity cwa
    WHERE cwa.fleet_id = p_fleet_id;

    -- 并发使用冲突
    IF active_count >= 3 THEN
        RETURN QUERY SELECT
            'concurrent_usage'::TEXT,
            CASE WHEN active_count >= 4 THEN 'high' ELSE 'medium' END::TEXT,
            format('%s 个成员同时活跃', active_count)::TEXT,
            (SELECT jsonb_agg(jsonb_build_object('nickname', nickname, 'member_id', id))
             FROM fleet_members WHERE fleet_id = p_fleet_id AND is_active = TRUE)::JSONB;
    END IF;

    -- 使用量接近限制
    IF total_tokens > usage_limit * 0.8 THEN
        RETURN QUERY SELECT
            'token_limit'::TEXT,
            CASE WHEN total_tokens > usage_limit * 0.9 THEN 'high' ELSE 'medium' END::TEXT,
            format('当前窗口使用量 %s/%s tokens (%.1f%%)', total_tokens, usage_limit, (total_tokens::FLOAT/usage_limit)*100)::TEXT,
            '{}'::JSONB;
    END IF;

    RETURN;
END;
$$ language 'plpgsql';

-- ============================================================================
-- 清理和维护函数
-- ============================================================================

-- 清理过期的冲突记录（保留最近30天）
CREATE OR REPLACE FUNCTION cleanup_old_conflicts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM usage_conflicts
    WHERE created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- 清理过期的使用记录（保留最近90天）
CREATE OR REPLACE FUNCTION cleanup_old_usage_records()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM fleet_usage_records
    WHERE timestamp < NOW() - INTERVAL '90 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- 重新计算成员总计
CREATE OR REPLACE FUNCTION recalculate_member_totals(p_member_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE fleet_members
    SET
        total_tokens_used = (
            SELECT COALESCE(SUM(total_tokens), 0)
            FROM fleet_usage_records
            WHERE member_id = p_member_id
        ),
        total_cost_incurred = (
            SELECT COALESCE(SUM(estimated_cost), 0)
            FROM fleet_usage_records
            WHERE member_id = p_member_id
        )
    WHERE id = p_member_id;
END;
$$ language 'plpgsql';

-- ============================================================================
-- 完成
-- ============================================================================

-- 创建完成提示
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Claude Fleet Management System 数据库初始化完成!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '已创建的表:';
    RAISE NOTICE '  - fleets (车队)';
    RAISE NOTICE '  - fleet_members (成员)';
    RAISE NOTICE '  - fleet_usage_records (使用记录)';
    RAISE NOTICE '  - fleet_window_stats (窗口统计)';
    RAISE NOTICE '  - usage_conflicts (冲突记录)';
    RAISE NOTICE '  - fleet_invitations (邀请)';
    RAISE NOTICE '';
    RAISE NOTICE '已创建的视图:';
    RAISE NOTICE '  - fleet_overview (车队概览)';
    RAISE NOTICE '  - current_window_activity (当前窗口活动)';
    RAISE NOTICE '  - active_conflicts (活跃冲突)';
    RAISE NOTICE '';
    RAISE NOTICE '已启用 Row Level Security (RLS)';
    RAISE NOTICE '请确保在应用程序中正确设置 app.current_user_token';
    RAISE NOTICE '=================================================';
END $$;
