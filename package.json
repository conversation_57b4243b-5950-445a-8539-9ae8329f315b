{"name": "ccusage", "type": "module", "version": "15.5.2", "description": "Usage analysis tool for Claude Code", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "funding": "https://github.com/ryoppippi/ccusage?sponsor=1", "homepage": "https://github.com/ryoppippi/ccusage#readme", "repository": {"type": "git", "url": "git+https://github.com/ryoppippi/ccusage.git"}, "bugs": {"url": "https://github.com/ryoppippi/ccusage/issues"}, "exports": {".": "./dist/index.js", "./calculate-cost": "./dist/calculate-cost.js", "./data-loader": "./dist/data-loader.js", "./debug": "./dist/debug.js", "./logger": "./dist/logger.js", "./mcp": "./dist/mcp.js", "./pricing-fetcher": "./dist/pricing-fetcher.js", "./package.json": "./package.json"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": "./dist/index.js", "files": ["dist"], "workspaces": [".", "docs"], "engines": {"node": ">=20.19.4"}, "scripts": {"build": "tsdown", "docs:build": "cd docs && bun run build", "docs:deploy": "cd docs && bun run deploy", "docs:dev": "cd docs && bun run dev", "docs:preview": "cd docs && bun run preview", "format": "bun run lint --fix", "lint": "eslint --cache .", "mcp": "bunx @modelcontextprotocol/inspector bun run start mcp", "prepack": "bun run build && clean-pkg-json", "prepare": "simple-git-hooks", "release": "bun lint && bun typecheck && vitest run && bun run build && bumpp", "start": "bun run ./src/index.ts", "test": "vitest", "typecheck": "tsgo --noEmit"}, "devDependencies": {"@antfu/utils": "^9.2.0", "@core/errorutil": "npm:@jsr/core__errorutil", "@hono/mcp": "^0.1.0", "@hono/node-server": "^1.17.1", "@jsr/std__async": "1", "@modelcontextprotocol/sdk": "^1.17.0", "@oxc-project/runtime": "^0.78.0", "@praha/byethrow": "^0.6.2", "@praha/byethrow-mcp": "^0.1.0", "@ryoppippi/eslint-config": "^0.3.7", "@types/bun": "^1.2.19", "@typescript/native-preview": "^7.0.0-dev.20250724.1", "ansi-escapes": "^7.0.0", "bumpp": "^10.2.0", "clean-pkg-json": "^1.3.0", "cli-table3": "^0.6.5", "consola": "^3.4.2", "es-toolkit": "^1.39.8", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "fast-sort": "^3.4.1", "fs-fixture": "^2.8.1", "gunshi": "^0.26.3", "hono": "^4.8.5", "lint-staged": "^16.1.2", "path-type": "^6.0.0", "picocolors": "^1.1.1", "pretty-ms": "^9.2.0", "publint": "^0.3.12", "simple-git-hooks": "^2.13.0", "sort-package-json": "^3.4.0", "string-width": "^7.2.0", "tinyglobby": "^0.2.14", "tsdown": "^0.13.0", "type-fest": "^4.41.0", "unplugin-macros": "^0.17.1", "unplugin-unused": "^0.5.1", "vitest": "^3.2.4", "xdg-basedir": "^5.1.0", "zod": "^3.25.67"}, "overrides": {"vite": "npm:rolldown-vite@latest"}, "simple-git-hooks": {"pre-commit": "bun lint-staged"}, "lint-staged": {"*": ["eslint --cache --fix"], "package.json": ["sort-package-json"]}, "trustedDependencies": ["simple-git-hooks"]}