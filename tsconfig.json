{
	"compilerOptions": {
		"target": "ESNext",
		"jsx": "react-jsx",
		// Environment setup & latest features
		"lib": [
			"ESNext"
		],
		"moduleDetection": "force",
		"module": "Preserve",
		// Bundler mode
		"moduleResolution": "bundler",
		"resolveJsonModule": true,
		"types": [
			"vitest/globals",
			"vitest/importMeta"
		],
		"allowImportingTsExtensions": true,
		"allowJs": true,
		// Best practices
		"strict": true,
		"noFallthroughCasesInSwitch": true,
		"noImplicitOverride": true,
		"noPropertyAccessFromIndexSignature": false,
		"noUncheckedIndexedAccess": true,
		// Some stricter flags (disabled by default)
		"noUnusedLocals": false,
		"noUnusedParameters": false,
		"noEmit": true,
		"verbatimModuleSyntax": true,
		"skipLibCheck": true
	},
	"exclude": [
		"dist"
	]
}
